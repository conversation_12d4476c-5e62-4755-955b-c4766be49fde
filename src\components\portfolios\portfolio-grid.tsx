"use client";

import { PortfolioCard } from "@/components/portfolios/portfolio-card";
import { PortfolioWithMetrics } from "@/utils/db/portfolio-queries";
import { cn } from "@/lib/utils";

interface PortfolioGridProps {
  portfolios: PortfolioWithMetrics[];
  className?: string;
  onAddTransaction?: (portfolioId: string) => void;
}

export function PortfolioGrid({
  portfolios,
  className,
  onAddTransaction,
}: PortfolioGridProps) {
  if (portfolios.length === 0) {
    return null;
  }

  return (
    <div
      className={cn(
        "grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
        className
      )}
      role="grid"
      aria-label="Lista portofoliilor"
    >
      {portfolios.map((portfolio) => (
        <div
          key={portfolio.id}
          role="gridcell"
          className="h-full focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 rounded-xl"
        >
          <PortfolioCard
            portfolio={portfolio}
            className="h-full"
            onAddTransaction={onAddTransaction}
          />
        </div>
      ))}
    </div>
  );
}
