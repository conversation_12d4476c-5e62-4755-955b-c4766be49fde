"use client";

import { AccountDetailsForm } from "@/components/account/account-details-form";
import {
  AccountSection,
  AccountSidebar,
} from "@/components/account/account-sidebar";
import { DeleteAccountSection } from "@/components/account/delete-account-section";
import { InactivePortfoliosSection } from "@/components/account/inactive-portfolios-section";
import { PasswordResetRequestForm } from "@/components/account/password-reset-request-form";

import { ProfilePictureUpload } from "@/components/account/profile-picture-upload";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useProfilePicture } from "@/hooks/use-profile-picture";
import {
  AccountDetailsFormDataFlexible,
  AccountDetailsUpdateData,
  getAccountCapabilities,
  getChangedFields,
  getImageSourceType,
  UserAccountData,
} from "@/lib/account-schemas";
import { authUtils } from "@/lib/auth-utils";
import { Portfolio } from "@/utils/db/portfolio-queries";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

interface MyAccountClientPageProps {
  initialAccountData: UserAccountData | null;
  user: {
    id: string;
    name: string;
    email: string;
    username?: string | null;
    image?: string | null;
    createdAt: Date;
    updatedAt: Date;
    emailVerified: boolean;
    displayUsername?: string | null;
  };
}

export default function MyAccountClientPage({
  initialAccountData,
  user,
}: MyAccountClientPageProps) {
  const [activeSection, setActiveSection] =
    useState<AccountSection>("detalii-cont");
  const [accountData, setAccountData] = useState<UserAccountData | null>(
    initialAccountData
  );
  const [isUpdating, setIsUpdating] = useState(false);
  const [uploadError, setUploadError] = useState<string>();
  const [profileUpdateError, setProfileUpdateError] = useState<string>();
  const [inactivePortfolios, setInactivePortfolios] = useState<Portfolio[]>([]);
  const [isLoadingInactivePortfolios, setIsLoadingInactivePortfolios] =
    useState(false);

  const { refreshProfilePicture } = useProfilePicture();

  const router = useRouter();

  const loadInactivePortfolios = useCallback(async () => {
    try {
      setIsLoadingInactivePortfolios(true);
      const response = await fetch("/api/portfolios/inactive");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Nu s-au putut încărca portofoliile inactive"
        );
      }

      const result = await response.json();
      setInactivePortfolios(result.portfolios || []);
    } catch (error) {
      console.error("Error loading inactive portfolios:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Nu s-au putut încărca portofoliile inactive";
      toast.error(errorMessage);
    } finally {
      setIsLoadingInactivePortfolios(false);
    }
  }, []);

  useEffect(() => {
    setProfileUpdateError(undefined);

    if (activeSection === "portofolii-inactive") {
      loadInactivePortfolios();
    }
  }, [activeSection, loadInactivePortfolios]);

  const refreshAccountData = async () => {
    try {
      const response = await fetch("/api/account/data");
      const result = await response.json();

      if (response.ok && result.success) {
        setAccountData(result.data);
      }
    } catch (error) {
      console.error("Error refreshing account data:", error);
    }
  };

  const handleSignOut = async () => {
    try {
      await authUtils.signOut(() => router.refresh());
    } catch (error) {
      console.error("Sign out error:", error);
      toast.error("Eroare la deconectare. Încearcă din nou.");
    }
  };

  const handleProfileUpdate = async (data: AccountDetailsFormDataFlexible) => {
    setIsUpdating(true);
    setProfileUpdateError(undefined);
    try {
      const originalData = accountData
        ? {
            email: accountData.email,
            username: accountData.username || "",
            name: accountData.name,
          }
        : {};

      const dataToCompare = {
        email: data.email,
        username: data.username || "",
        name: data.name,
      };

      const changedFields = getChangedFields(
        dataToCompare,
        originalData
      ) as AccountDetailsUpdateData;

      if (
        capabilities.isGoogleAccount &&
        !accountData?.username &&
        !data.username?.trim()
      ) {
        delete changedFields.username;
      }

      if (Object.keys(changedFields).length === 0) {
        setIsUpdating(false);
        toast.info("Nu există modificări de salvat.");
        return;
      }

      const response = await fetch("/api/account/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(changedFields),
      });

      const result = await response.json();

      if (!response.ok) {
        const errorMessage =
          result.error || "Eroare la actualizarea profilului";
        setProfileUpdateError(errorMessage);
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }

      await refreshAccountData();
      toast.success("Profilul a fost actualizat cu succes!");
    } catch (error) {
      console.error("Error updating profile:", error);
      if (!profileUpdateError) {
        setProfileUpdateError(
          error instanceof Error ? error.message : "A apărut o eroare"
        );
      }
      return error;
    } finally {
      setIsUpdating(false);
    }
  };

  const handleImageUpload = async (file: File) => {
    setIsUpdating(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/account/profile-picture", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        const errorMessage = `${result.error}. ${
          result.details.length > 0 ? result.details[0].message : ""
        }`;
        setUploadError(errorMessage);
        toast.error(errorMessage);
        throw new Error(result.error || "Eroare la încărcarea imaginii");
      }

      setUploadError(undefined);
      await refreshAccountData();
      toast.success("Imaginea de profil a fost încărcată cu succes!");
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Eroare la încărcarea imaginii!");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleImageRemove = async () => {
    if (!accountData?.profile_picture?.user_id) return;

    setIsUpdating(true);
    try {
      const response = await fetch("/api/account/profile-picture", {
        method: "DELETE",
      });

      const result = await response.json();

      if (!response.ok) {
        const errorMessage = result.error || "Eroare la ștergerea imaginii";
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }

      await refreshAccountData();
      toast.success("Imaginea de profil a fost ștearsă cu succes!");
    } catch (error) {
      console.error("Error removing image:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  if (isUpdating && !accountData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-portavio-orange mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Se încarcă...</p>
        </div>
      </div>
    );
  }

  const capabilities = accountData
    ? getAccountCapabilities(accountData.accounts || [])
    : {
        canChangeEmail: false,
        canChangePassword: false,
        canChangeUsername: true,
        canChangeName: true,
        canUploadProfilePicture: true,
        isGoogleAccount: false,
      };

  const currentImage =
    accountData?.profile_picture?.image_data ||
    accountData?.image ||
    user?.image ||
    undefined;

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8 flex-wrap gap-4">
            <div>
              <h1 className="text-5xl font-bold text-foreground">Contul meu</h1>
            </div>
            <Button onClick={handleSignOut} variant="outline">
              Deconectează-te
            </Button>
          </div>

          <div className="flex flex-col lg:flex-row gap-0">
            <AccountSidebar
              activeSection={activeSection}
              onSectionChange={setActiveSection}
            />

            <div className="flex-1">
              <Card className="shadow-lg rounded-none lg:rounded-br-lg lg:rounded-tr-lg lg:rounded-bl-none rounded-bl-lg rounded-br-lg h-full">
                <CardHeader>
                  <CardTitle className="text-xl">
                    {activeSection === "detalii-cont" && "Detalii cont"}
                    {activeSection === "schimbare-parola" && "Schimbare parolă"}
                    {activeSection === "portofolii-inactive" &&
                      "Portofolii inactive"}
                    {activeSection === "delete-account" && "Ștergere cont"}
                  </CardTitle>
                  <CardDescription>
                    {activeSection === "detalii-cont" &&
                      "Gestionează informațiile contului tău"}
                    {activeSection === "schimbare-parola" &&
                      "Resetează parola contului tău"}
                    {activeSection === "portofolii-inactive" &&
                      "Gestionează portofoliile dezactivate și reactivează-le"}
                    {activeSection === "delete-account" &&
                      "Șterge permanent contul și toate datele asociate"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {activeSection === "detalii-cont" && accountData && (
                    <div className="lg:flex gap-16 justify-between w-full">
                      <div className="flex-4">
                        <AccountDetailsForm
                          initialData={{
                            email: accountData.email,
                            username: accountData.username || "",
                            name: accountData.name,
                            createdAt: accountData.createdAt,
                            emailVerified: user.emailVerified,
                          }}
                          capabilities={capabilities}
                          onSubmit={handleProfileUpdate}
                          isLoading={isUpdating}
                          error={profileUpdateError}
                        />
                      </div>

                      <div className="flex flex-col items-center flex-3">
                        <ProfilePictureUpload
                          currentImage={currentImage}
                          username={accountData.username || undefined}
                          name={accountData.name}
                          onImageUpload={handleImageUpload}
                          onImageRemove={
                            getImageSourceType(currentImage) === "base64"
                              ? handleImageRemove
                              : undefined
                          }
                          onImageChange={refreshProfilePicture}
                          isLoading={isUpdating}
                        />
                        {uploadError && (
                          <p className="text-sm text-red-600 dark:text-red-400">
                            {uploadError}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {activeSection === "schimbare-parola" && accountData && (
                    <PasswordResetRequestForm
                      userEmail={accountData.email}
                      canChangePassword={capabilities.canChangePassword}
                    />
                  )}

                  {activeSection === "portofolii-inactive" && (
                    <InactivePortfoliosSection
                      inactivePortfolios={inactivePortfolios}
                      onPortfolioReactivated={loadInactivePortfolios}
                      isLoading={isLoadingInactivePortfolios}
                    />
                  )}

                  {activeSection === "delete-account" && accountData && (
                    <DeleteAccountSection accountData={accountData} />
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
