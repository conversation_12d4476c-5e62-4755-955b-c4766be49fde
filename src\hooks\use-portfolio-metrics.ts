"use client";

import { useQuery } from "@tanstack/react-query";
import { PortfolioMetrics } from "@/utils/db/dashboard-queries";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";

// Query keys for portfolio metrics
export const portfolioMetricsKeys = {
  all: ["portfolio-metrics"] as const,
  lists: () => [...portfolioMetricsKeys.all, "list"] as const,
  list: (portfolioIds: string[], displayCurrency: SupportedCurrency) =>
    [
      ...portfolioMetricsKeys.lists(),
      { portfolioIds, displayCurrency },
    ] as const,
};

// API response interface
interface PortfolioMetricsResponse {
  success: boolean;
  data: PortfolioMetrics;
  message: string;
}

// Fetch function for portfolio metrics data
async function fetchPortfolioMetrics(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
): Promise<PortfolioMetrics> {
  if (portfolioIds.length === 0) {
    return {
      capitalInvested: 0,
      priceGain: 0,
      priceGainPercentage: 0,
      dividends: 0,
      dividendsPercentage: 0,
      realizedGain: 0,
      realizedGainPercentage: 0,
      transactionCosts: 0,
      taxes: 0,
      totalReturn: 0,
      twrr: 0,
      mwrr: 0,
      displayCurrency,
    };
  }

  // Use GET for smaller lists, POST for larger ones - more than 4 portfolios
  const usePost = portfolioIds.length > 4;

  let response: Response;

  if (usePost) {
    response = await fetch("/api/dashboard/portfolio-metrics", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ portfolioIds, displayCurrency }),
    });
  } else {
    const params = new URLSearchParams({
      portfolioIds: portfolioIds.join(","),
      displayCurrency,
    });
    response = await fetch(`/api/dashboard/portfolio-metrics?${params}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error ||
        "Nu s-au putut încărca datele de metrici ale portofoliului"
    );
  }

  const result: PortfolioMetricsResponse = await response.json();
  return result.data;
}

// Hook for fetching portfolio metrics
export function usePortfolioMetrics(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
) {
  return useQuery<PortfolioMetrics>({
    queryKey: portfolioMetricsKeys.list(portfolioIds, displayCurrency),
    queryFn: () => fetchPortfolioMetrics(portfolioIds, displayCurrency),
    enabled: portfolioIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error instanceof Error && error.message.includes("autentificat")) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
}
