"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { usePortfolioMetrics } from "@/hooks/use-portfolio-metrics";
import { usePortfolioPerformance } from "@/hooks/use-portfolio-performance";
import { <PERSON><PERSON><PERSON> } from "@/components/ui/bar-chart";
import { Info, Loader2, TrendingDown, TrendingUp } from "lucide-react";
import { SupportedCurrency } from "./currency-selector";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

interface PortfolioMetricsCardProps {
  selectedPortfolios: string[];
  displayCurrency: SupportedCurrency;
}

const formatCurrency = (value: number, currency: SupportedCurrency): string => {
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(2)}%`;
};

const getPercentageColor = (value: number): string => {
  if (value > 0) return "text-green-600";
  if (value < 0) return "text-red-600";
  return "text-gray-600";
};

export function PortfolioMetricsCard({
  selectedPortfolios,
  displayCurrency,
}: PortfolioMetricsCardProps) {
  const {
    data: metrics,
    isLoading,
    error,
  } = usePortfolioMetrics(selectedPortfolios, displayCurrency);

  // Fetch full-range performance to compute YoY bars
  const { data: performanceMax } = usePortfolioPerformance(
    selectedPortfolios,
    "MAX",
    displayCurrency
  );

  if (isLoading) {
    return (
      <Card className="h-[650px] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <CardTitle className="text-xl">Performanță</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            Se încarcă datele...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-[650px] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <CardTitle className="text-xl">Performanță</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <p className="text-red-500 mb-2">Eroare la încărcarea datelor</p>
            <p className="text-sm">
              {error instanceof Error ? error.message : "Eroare necunoscută"}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!metrics) {
    return (
      <Card className="h-[650px] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <CardTitle className="text-xl">Performanță</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <p>Nu există date de performanță disponibile</p>
            <p className="text-sm mt-2">
              Selectează portofolii cu tranzacții pentru a vedea performanța
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }
  // Year-over-year performance bars (last 5 years)

  const nowYear = new Date().getFullYear();
  const yearsAsc = Array.from({ length: 5 }, (_, i) => nowYear - 4 + i);

  // Build year-end portfolio values per year
  const lastValueByYear = new Map<number, number>();
  performanceMax?.data?.forEach((p: any) => {
    const y = new Date(p.date).getFullYear();
    // Data is ascending; the last assignment for a year will be its year-end value
    lastValueByYear.set(y, p.value);
  });

  const yearlyBars = yearsAsc.map((y) => {
    const curr = lastValueByYear.get(y);
    const prev = lastValueByYear.get(y - 1);
    const change = curr !== undefined && prev !== undefined ? curr - prev : 0;
    const pct = prev && prev !== 0 ? (change / prev) * 100 : 0;
    return {
      year: y.toString(),
      Pozitiv: change > 0 ? change : 0,
      Negativ: change < 0 ? change : 0,
      changeAbs: change,
      changePct: pct,
    };
  });

  const yoyValueFormatter = (value: number) =>
    formatCurrency(value, displayCurrency);

  return (
    <Card className="h-[650px] flex flex-col">
      <CardHeader className="flex-shrink-0">
        <CardTitle className="text-xl">Performanță</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto space-y-6">
        {/* Capital Section */}
        <div>
          <h3 className="text-lg font-semibold text-portavio-blue mb-3">
            Capital
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">
                Capital investit
              </span>
              <span className="font-medium">
                {formatCurrency(metrics.capitalInvested, displayCurrency)}
              </span>
            </div>
          </div>
        </div>

        {/* Performance Breakdown Section */}
        <div>
          <h3 className="text-lg font-semibold text-portavio-blue mb-3">
            Analiza performanței
          </h3>
          <div className="space-y-2">
            {/* Price gain row */}
            <div className="grid grid-cols-3 items-center gap-4">
              <span className="text-sm text-muted-foreground text-left">
                Câștig din preț
              </span>
              <div className="flex items-center justify-center gap-1">
                <span
                  className={`text-sm flex items-center gap-1 ${getPercentageColor(
                    metrics.priceGainPercentage
                  )}`}
                >
                  {metrics.priceGainPercentage >= 0 ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  {formatPercentage(metrics.priceGainPercentage)}
                </span>
              </div>
              <span className="font-medium text-right">
                {formatCurrency(metrics.priceGain, displayCurrency)}
              </span>
            </div>

            {/* Dividends row */}
            <div className="grid grid-cols-3 items-center gap-4">
              <span className="text-sm text-muted-foreground text-left">
                Dividends
              </span>
              <div className="flex items-center justify-center gap-1">
                <span
                  className={`text-sm flex items-center gap-1 ${getPercentageColor(
                    metrics.dividendsPercentage
                  )}`}
                >
                  {metrics.dividendsPercentage >= 0 ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  {formatPercentage(metrics.dividendsPercentage)}
                </span>
              </div>
              <span className="font-medium text-right">
                {formatCurrency(metrics.dividends, displayCurrency)}
              </span>
            </div>

            {/* Realized gain row */}
            <div className="grid grid-cols-3 items-center gap-4">
              <span className="text-sm text-muted-foreground text-left">
                Câștig realizat
              </span>
              <div className="flex items-center justify-center gap-1">
                <span
                  className={`text-sm flex items-center gap-1 ${getPercentageColor(
                    metrics.realizedGainPercentage
                  )}`}
                >
                  {metrics.realizedGainPercentage >= 0 ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  {formatPercentage(metrics.realizedGainPercentage)}
                </span>
              </div>
              <span className="font-medium text-right">
                {formatCurrency(metrics.realizedGain, displayCurrency)}
              </span>
            </div>
          </div>
        </div>

        {/* Transaction Costs Section */}
        <div>
          <h3 className="text-lg font-semibold text-portavio-blue mb-3">
            Costuri de tranzacție
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">
                Taxe / Comisioane
              </span>
              <span className="font-medium">
                {formatCurrency(metrics.taxes, displayCurrency)}
              </span>
            </div>
          </div>
        </div>

        {/* Total Return Section */}
        <div className="border-t pt-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-portavio-blue">
              Câștig total
            </h3>
            <span className="text-xl font-bold text-portavio-blue">
              {formatCurrency(metrics.totalReturn, displayCurrency)}
            </span>
          </div>

          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-portavio-blue">
              Valoarea totală
            </h3>
            <span className="text-xl font-bold text-portavio-blue">
              {formatCurrency(
                metrics.capitalInvested + metrics.priceGain + metrics.dividends,
                displayCurrency
              )}
            </span>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="font-medium text-sm">
              True Time-Weighted Rate of Return (TWRR)
            </span>
            <span className="font-semibold">
              {formatPercentage(metrics.twrr)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="font-medium text-sm">
              Money-Weighted Rate of Return (MWRR)
            </span>
            <span className="font-semibold">
              {formatPercentage(metrics.mwrr)}
            </span>
          </div>
        </div>

        {/* Yearly Performance Bar Chart */}
        <div className="pt-4 border-t">
          <h3 className="text-lg flex items-center gap-2 flex-wrap font-semibold text-portavio-blue mb-3">
            Evoluție anuală{" "}
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 inline-block ml-1" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-[200px]">
                  Evoluția portofoliului pe an, bazată pe valoarea la{" "}
                  <strong>începutul</strong> și la <strong>sfârșitul</strong>{" "}
                  fiecărui an, excluzând dividendele.
                </p>
              </TooltipContent>
            </Tooltip>
          </h3>
          <div className="h-56">
            <BarChart
              data={yearlyBars}
              index="year"
              categories={["Pozitiv", "Negativ"]}
              colors={["success", "error"]}
              valueFormatter={yoyValueFormatter}
              showLegend={true}
              showGridLines={true}
              showXAxis={true}
              showYAxis={false}
              autoMinValue={true}
              allowDecimals={false}
              barCategoryGap={12}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
