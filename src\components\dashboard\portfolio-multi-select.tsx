"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { usePortfoliosWithMetrics } from "@/hooks/use-portfolios-query";
import { ChevronDown, FolderOpen } from "lucide-react";
import { useEffect, useState, useCallback, useRef } from "react";

interface PortfolioMultiSelectProps {
  selectedPortfolios: string[];
  onSelectionChange: (portfolioIds: string[]) => void;
  className?: string;
  debounceMs?: number;
}

export function PortfolioMultiSelect({
  selectedPortfolios,
  onSelectionChange,
  className,
  debounceMs = 500,
}: PortfolioMultiSelectProps) {
  const { data: portfolios, isLoading, error } = usePortfoliosWithMetrics();
  const [isOpen, setIsOpen] = useState(false);
  const [localSelection, setLocalSelection] =
    useState<string[]>(selectedPortfolios);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedOnSelectionChange = useCallback(
    (portfolioIds: string[]) => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      debounceTimeoutRef.current = setTimeout(() => {
        onSelectionChange(portfolioIds);
      }, debounceMs);
    },
    [onSelectionChange, debounceMs]
  );

  useEffect(() => {
    setLocalSelection(selectedPortfolios);
  }, [selectedPortfolios]);

  // Initial auto-selection effect (immediate, no debounce needed)
  useEffect(() => {
    if (
      portfolios?.portfolios &&
      portfolios.portfolios.length > 0 &&
      selectedPortfolios.length === 0
    ) {
      const allPortfolioIds = portfolios.portfolios.map((p) => p.id);
      setLocalSelection(allPortfolioIds);
      onSelectionChange(allPortfolioIds); // Immediate for initial load
    }
  }, [portfolios?.portfolios, selectedPortfolios.length, onSelectionChange]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const handlePortfolioToggle = (
    portfolioId: string,
    event?: React.MouseEvent
  ) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    const isSelected = localSelection.includes(portfolioId);
    let newSelection: string[];

    if (isSelected) {
      // Remove from selection
      newSelection = localSelection.filter((id) => id !== portfolioId);
    } else {
      // Add to selection
      newSelection = [...localSelection, portfolioId];
    }

    setLocalSelection(newSelection);
    debouncedOnSelectionChange(newSelection);
  };

  const handleSelectAll = (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    if (!portfolios?.portfolios) return;

    const allPortfolioIds = portfolios.portfolios.map((p) => p.id);
    setLocalSelection(allPortfolioIds);
    debouncedOnSelectionChange(allPortfolioIds);
  };

  const getDisplayText = () => {
    if (!portfolios?.portfolios || portfolios.portfolios.length === 0) {
      return "Nu există portofolii";
    }

    if (localSelection.length === 0) {
      return "Selectează portofolii";
    }

    if (localSelection.length === portfolios.portfolios.length) {
      return "Toate portofoliile";
    }

    if (localSelection.length === 1) {
      const portfolio = portfolios.portfolios.find(
        (p) => p.id === localSelection[0]
      );
      return portfolio?.name || "1 portofoliu";
    }

    return `${localSelection.length} portofolii`;
  };

  if (isLoading) {
    return (
      <div className={className}>
        <Button variant="outline" disabled className="w-full justify-between">
          <span className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Se încarcă portofoliile...
          </span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  if (error || !portfolios) {
    return (
      <div className={className}>
        <Button variant="outline" disabled className="w-full justify-between">
          <span className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Eroare la încărcarea portofoliilor
          </span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className={className}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-full justify-between">
            <span className="flex items-center gap-2">
              <FolderOpen className="h-4 w-4" />
              {getDisplayText()}
            </span>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-80" align="start">
          <DropdownMenuLabel className="flex items-center justify-between">
            <span>Selectează Portofolii</span>
            <div className="flex gap-1">
              <Badge variant="secondary" className="text-xs">
                {localSelection.length}/{portfolios.portfolios.length}
              </Badge>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />

          <div className="flex gap-1 p-1">
            <Button
              variant="ghost"
              size="sm"
              className="flex-1 h-8 text-xs"
              onClick={handleSelectAll}
              disabled={localSelection.length === portfolios.portfolios.length}
            >
              Selectează Tot
            </Button>
          </div>

          <DropdownMenuSeparator />

          <div className="max-h-60 overflow-y-auto">
            {portfolios.portfolios.map((portfolio) => (
              <div
                key={portfolio.id}
                className="flex items-start space-x-2 px-2 py-2 hover:bg-accent hover:text-accent-foreground cursor-pointer rounded-sm"
                onClick={(e) => handlePortfolioToggle(portfolio.id, e)}
              >
                <Checkbox
                  id={`portfolio-${portfolio.id}`}
                  checked={localSelection.includes(portfolio.id)}
                  onCheckedChange={(checked) => {
                    let newSelection: string[];
                    if (checked) {
                      if (!localSelection.includes(portfolio.id)) {
                        newSelection = [...localSelection, portfolio.id];
                      } else {
                        return; // Already selected, no change needed
                      }
                    } else {
                      newSelection = localSelection.filter(
                        (id) => id !== portfolio.id
                      );
                    }
                    setLocalSelection(newSelection);
                    debouncedOnSelectionChange(newSelection);
                  }}
                  onClick={(e) => e.stopPropagation()}
                  className="mt-1"
                />
                <label
                  htmlFor={`portfolio-${portfolio.id}`}
                  className="flex flex-col gap-1 min-w-0 flex-1 cursor-pointer"
                  onClick={(e) => e.preventDefault()}
                >
                  <span className="font-medium truncate">{portfolio.name}</span>
                  {portfolio.description && (
                    <span className="text-xs text-muted-foreground truncate hover:text-foreground">
                      {portfolio.description}
                    </span>
                  )}
                </label>
              </div>
            ))}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
