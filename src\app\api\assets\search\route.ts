import { NextRequest, NextResponse } from "next/server";
import { searchAssets } from "@/utils/db/asset-queries";
import { z } from "zod";

// Validation schema for search parameters
const searchParamsSchema = z.object({
  q: z
    .string()
    .min(1, "Termenul de căutare trebuie să aibă cel puțin 1 caracter")
    .max(50, "Termenul de căutare nu poate avea mai mult de 50 de caractere"),
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 15))
    .refine(
      (val) => val >= 1 && val <= 50,
      "Limita trebuie să fie între 1 și 50"
    ),
});

// GET /api/assets/search - Search for assets
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    const limitParam = searchParams.get("limit");

    // Validate search parameters
    let validatedParams;
    try {
      validatedParams = searchParamsSchema.parse({
        q: query,
        limit: limitParam,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            error: "Parametri invalizi",
            details: error.errors.map((err) => ({
              field: err.path.join("."),
              message: err.message,
            })),
          },
          { status: 400 }
        );
      }
      throw error;
    }

    // Search for assets
    const assets = await searchAssets(validatedParams.q, validatedParams.limit);

    return NextResponse.json({
      success: true,
      data: assets,
      count: assets.length,
      query: validatedParams.q,
      limit: validatedParams.limit,
    });
  } catch (error) {
    console.error("Error in asset search API:", error);

    // Handle specific error types
    if (error instanceof Error) {
      return NextResponse.json(
        {
          error: error.message,
          success: false,
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        error: "A apărut o eroare neașteptată la căutarea activelor",
        success: false,
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}
