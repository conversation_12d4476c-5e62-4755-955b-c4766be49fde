"use client";

import { useQuery } from "@tanstack/react-query";
import { PortfolioComposition } from "@/utils/db/dashboard-queries";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";

// Query keys for portfolio composition
export const portfolioCompositionKeys = {
  all: ["portfolio-composition"] as const,
  lists: () => [...portfolioCompositionKeys.all, "list"] as const,
  list: (portfolioIds: string[], displayCurrency: SupportedCurrency) =>
    [
      ...portfolioCompositionKeys.lists(),
      { portfolioIds, displayCurrency },
    ] as const,
};

// API response interface
interface PortfolioCompositionResponse {
  success: boolean;
  data: PortfolioComposition;
  message: string;
}

// Fetch portfolio composition data from API
async function fetchPortfolioComposition(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
): Promise<PortfolioComposition> {
  if (portfolioIds.length === 0) {
    throw new Error("Cel puțin un portofoliu trebuie selectat");
  }

  // Use GET for smaller lists, POST for larger ones - more than 4 portfolios
  const usePost = portfolioIds.length > 4;

  let response: Response;

  if (usePost) {
    response = await fetch("/api/dashboard/portfolio-composition", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ portfolioIds, displayCurrency }),
    });
  } else {
    const params = new URLSearchParams({
      portfolioIds: portfolioIds.join(","),
      displayCurrency,
    });
    response = await fetch(`/api/dashboard/portfolio-composition?${params}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error ||
        "Nu s-au putut încărca datele de compoziție ale portofoliului"
    );
  }

  const result: PortfolioCompositionResponse = await response.json();
  return result.data;
}

// Hook for fetching portfolio composition
export function usePortfolioComposition(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
) {
  return useQuery<PortfolioComposition>({
    queryKey: portfolioCompositionKeys.list(portfolioIds, displayCurrency),
    queryFn: () => fetchPortfolioComposition(portfolioIds, displayCurrency),
    enabled: portfolioIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error instanceof Error && error.message.includes("autentificat")) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
}
