"use client";

import { ForgotPasswordForm } from "@/components/auth/forgot-password-form";
import { GoogleSignInButton } from "@/components/auth/google-signin-button";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRedirectIfAuthenticated } from "@/hooks/use-auth";
import { signInSchema, type SignInFormData } from "@/lib/auth-schemas";
import { authUtils } from "@/lib/auth-utils";
import { useSession } from "@/lib/auth-client";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";

export function SignInForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const router = useRouter();
  const { data: session } = useSession();

  useRedirectIfAuthenticated();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
  });

  const onSubmit = async (data: SignInFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      await authUtils.signIn(data);
      router.push("/profile");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-[80vh] items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">
            {showForgotPassword ? "Resetare Parolă" : "Conectează-te"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {showForgotPassword ? (
            <ForgotPasswordForm
              onBack={() => setShowForgotPassword(false)}
              prefillEmail={session?.user?.email}
            />
          ) : (
            <>
              <div className="space-y-4">
                <GoogleSignInButton variant="signin" />

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Sau continuă cu
                    </span>
                  </div>
                </div>
              </div>

              <form
                onSubmit={handleSubmit(onSubmit)}
                className="space-y-4 mt-4"
              >
                {error && (
                  <div className="rounded-md bg-red-50 p-3 text-sm text-red-600 dark:text-red-400 dark:bg-red-900/20">
                    {error}
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="identifier">Email sau Username</Label>
                  <Input
                    id="identifier"
                    type="text"
                    placeholder="<EMAIL> sau username"
                    {...register("identifier")}
                    className={
                      errors.identifier
                        ? "border-red-500 dark:border-red-400"
                        : ""
                    }
                  />
                  {errors.identifier && (
                    <p className="text-sm text-red-600 dark:text-red-400">
                      {errors.identifier.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Parola</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    {...register("password")}
                    className={
                      errors.password
                        ? "border-red-500 dark:border-red-400"
                        : ""
                    }
                  />
                  {errors.password && (
                    <p className="text-sm text-red-600 dark:text-red-400">
                      {errors.password.message}
                    </p>
                  )}
                </div>

                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? "Se conectează..." : "Conectează-te"}
                </Button>
              </form>

              <div className="mt-4 text-center">
                <button
                  type="button"
                  onClick={() => setShowForgotPassword(true)}
                  className="text-sm text-portavio-orange hover:underline font-medium cursor-pointer"
                >
                  Ai uitat parola?
                </button>
              </div>

              <div className="mt-6 text-center text-sm">
                <span className="text-muted-foreground">Nu ai cont? </span>
                <Link
                  href="/auth/signup"
                  className="text-portavio-orange hover:underline font-medium"
                >
                  Creează cont
                </Link>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
