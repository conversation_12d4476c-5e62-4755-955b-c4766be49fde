/**
 * Test suite for calculateCompanyHoldings function
 * Run with: node src/tests/test-company-holdings.js
 */

// Mock functions
function calculateCompanyHoldings(transactions) {
  const holdings = new Map();

  transactions.forEach((transaction) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || { shares: 0, buyTransactions: [], allTransactions: [] };
    
    // Track all transactions for this ticker
    existing.allTransactions.push(transaction);
    
    // Calculate net shares
    if (transaction.transaction_type === "BUY") {
      existing.shares += transaction.quantity;
      existing.buyTransactions.push(transaction);
    } else if (transaction.transaction_type === "SELL") {
      existing.shares -= transaction.quantity;
    }
    
    holdings.set(ticker, existing);
  });

  // Filter out holdings with zero or negative shares
  const filteredHoldings = new Map();
  holdings.forEach((holding, ticker) => {
    if (holding.shares > 0) {
      filteredHoldings.set(ticker, holding);
    }
  });

  return filteredHoldings;
}

function calculateAverageCost(buyTransactions) {
  if (buyTransactions.length === 0) return 0;
  
  let totalCost = 0;
  let totalShares = 0;
  
  buyTransactions.forEach((transaction) => {
    totalCost += transaction.price * transaction.quantity;
    totalShares += transaction.quantity;
  });
  
  return totalShares > 0 ? totalCost / totalShares : 0;
}

function calculateCostValue(buyTransactions) {
  return buyTransactions.reduce((total, transaction) => {
    return total + (transaction.price * transaction.quantity);
  }, 0);
}

// Test data
const mockTransactions = [
  {
    id: "1",
    ticker: "AAPL",
    transaction_type: "BUY",
    quantity: 10,
    price: 150.00,
    transaction_date: "2024-01-15",
  },
  {
    id: "2",
    ticker: "AAPL",
    transaction_type: "BUY",
    quantity: 5,
    price: 160.00,
    transaction_date: "2024-02-15",
  },
  {
    id: "3",
    ticker: "AAPL",
    transaction_type: "SELL",
    quantity: 3,
    price: 170.00,
    transaction_date: "2024-03-15",
  },
  {
    id: "4",
    ticker: "NVDA",
    transaction_type: "BUY",
    quantity: 20,
    price: 800.00,
    transaction_date: "2024-01-20",
  },
  {
    id: "5",
    ticker: "NVDA",
    transaction_type: "SELL",
    quantity: 5,
    price: 900.00,
    transaction_date: "2024-04-20",
  },
  {
    id: "6",
    ticker: "TSLA",
    transaction_type: "BUY",
    quantity: 8,
    price: 200.00,
    transaction_date: "2024-02-01",
  },
  {
    id: "7",
    ticker: "TSLA",
    transaction_type: "SELL",
    quantity: 8,
    price: 250.00,
    transaction_date: "2024-05-01",
  },
];

// Test functions
function assertEqual(actual, expected, testName, tolerance = 0.01) {
  if (Math.abs(actual - expected) < tolerance) {
    console.log(`✅ PASS: ${testName}`);
    return true;
  } else {
    console.log(`❌ FAIL: ${testName}`);
    console.log(`   Expected: ${expected}, Actual: ${actual}`);
    return false;
  }
}

function assertMapSize(map, expectedSize, testName) {
  if (map.size === expectedSize) {
    console.log(`✅ PASS: ${testName} - Map size: ${map.size}`);
    return true;
  } else {
    console.log(`❌ FAIL: ${testName}`);
    console.log(`   Expected size: ${expectedSize}, Actual size: ${map.size}`);
    return false;
  }
}

// Run tests
console.log("🧪 Testing calculateCompanyHoldings function\n");

// Test 1: Basic holdings calculation
console.log("Test 1: Basic company holdings calculation");
const holdings = calculateCompanyHoldings(mockTransactions);

console.log("Holdings summary:");
holdings.forEach((holding, ticker) => {
  console.log(`${ticker}: ${holding.shares} shares, ${holding.buyTransactions.length} buy transactions`);
});

// Test 2: Holdings map size (should exclude TSLA as it was fully sold)
console.log("\nTest 2: Holdings map size");
assertMapSize(holdings, 2, "Holdings map size (excluding fully sold positions)");

// Test 3: AAPL holdings
console.log("\nTest 3: AAPL holdings");
const aaplHolding = holdings.get("AAPL");
if (aaplHolding) {
  assertEqual(aaplHolding.shares, 12, "AAPL shares count"); // 10 + 5 - 3 = 12
  assertEqual(aaplHolding.buyTransactions.length, 2, "AAPL buy transactions count");
  assertEqual(aaplHolding.allTransactions.length, 3, "AAPL all transactions count");
} else {
  console.log("❌ FAIL: AAPL holding not found");
}

// Test 4: NVDA holdings
console.log("\nTest 4: NVDA holdings");
const nvdaHolding = holdings.get("NVDA");
if (nvdaHolding) {
  assertEqual(nvdaHolding.shares, 15, "NVDA shares count"); // 20 - 5 = 15
  assertEqual(nvdaHolding.buyTransactions.length, 1, "NVDA buy transactions count");
  assertEqual(nvdaHolding.allTransactions.length, 2, "NVDA all transactions count");
} else {
  console.log("❌ FAIL: NVDA holding not found");
}

// Test 5: TSLA should not be in holdings (fully sold)
console.log("\nTest 5: TSLA fully sold position");
const tslaHolding = holdings.get("TSLA");
if (!tslaHolding) {
  console.log("✅ PASS: TSLA not in holdings (fully sold)");
} else {
  console.log("❌ FAIL: TSLA should not be in holdings");
  console.log(`   TSLA shares: ${tslaHolding.shares}`);
}

// Test 6: Average cost calculation
console.log("\nTest 6: Average cost calculation");
if (aaplHolding) {
  const aaplAvgCost = calculateAverageCost(aaplHolding.buyTransactions);
  // (10 * 150 + 5 * 160) / 15 = (1500 + 800) / 15 = 153.33
  assertEqual(aaplAvgCost, 153.33, "AAPL average cost", 0.1);
}

if (nvdaHolding) {
  const nvdaAvgCost = calculateAverageCost(nvdaHolding.buyTransactions);
  assertEqual(nvdaAvgCost, 800.00, "NVDA average cost");
}

// Test 7: Cost value calculation
console.log("\nTest 7: Cost value calculation");
if (aaplHolding) {
  const aaplCostValue = calculateCostValue(aaplHolding.buyTransactions);
  // 10 * 150 + 5 * 160 = 1500 + 800 = 2300
  assertEqual(aaplCostValue, 2300, "AAPL cost value");
}

if (nvdaHolding) {
  const nvdaCostValue = calculateCostValue(nvdaHolding.buyTransactions);
  assertEqual(nvdaCostValue, 16000, "NVDA cost value"); // 20 * 800
}

// Test 8: Empty transactions array
console.log("\nTest 8: Empty transactions array");
const emptyHoldings = calculateCompanyHoldings([]);
assertMapSize(emptyHoldings, 0, "Empty transactions holdings");

// Test 9: Only BUY transactions
console.log("\nTest 9: Only BUY transactions");
const buyOnlyTransactions = mockTransactions.filter(t => t.transaction_type === "BUY");
const buyOnlyHoldings = calculateCompanyHoldings(buyOnlyTransactions);
assertMapSize(buyOnlyHoldings, 3, "Buy-only holdings size");

const aaplBuyOnly = buyOnlyHoldings.get("AAPL");
if (aaplBuyOnly) {
  assertEqual(aaplBuyOnly.shares, 15, "AAPL buy-only shares"); // 10 + 5
}

// Test 10: Single transaction
console.log("\nTest 10: Single transaction");
const singleTransaction = [mockTransactions[0]];
const singleHoldings = calculateCompanyHoldings(singleTransaction);
assertMapSize(singleHoldings, 1, "Single transaction holdings");

const singleAapl = singleHoldings.get("AAPL");
if (singleAapl) {
  assertEqual(singleAapl.shares, 10, "Single transaction AAPL shares");
  assertEqual(singleAapl.buyTransactions.length, 1, "Single transaction buy count");
}

// Test 11: Edge case - sell more than owned (negative shares)
console.log("\nTest 11: Oversell scenario");
const oversellTransactions = [
  {
    id: "1",
    ticker: "TEST",
    transaction_type: "BUY",
    quantity: 5,
    price: 100.00,
    transaction_date: "2024-01-15",
  },
  {
    id: "2",
    ticker: "TEST",
    transaction_type: "SELL",
    quantity: 10,
    price: 110.00,
    transaction_date: "2024-02-15",
  },
];

const oversellHoldings = calculateCompanyHoldings(oversellTransactions);
assertMapSize(oversellHoldings, 0, "Oversell holdings (should be filtered out)");

console.log("\n🏁 Test suite completed!");
