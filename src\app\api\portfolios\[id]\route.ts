import { auth } from "@/lib/auth";
import { portfolioEditSchema } from "@/lib/portfolio-schemas";
import {
  getPortfolioById,
  updatePortfolio,
  deletePortfolio,
} from "@/utils/db/portfolio-queries";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

// GET /api/portfolios/[id] - Get a specific portfolio
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const { id } = await params;
    const portfolioId = id;

    if (!portfolioId) {
      return NextResponse.json(
        { error: "ID-ul portofoliului este obligatoriu" },
        { status: 400 }
      );
    }

    const portfolio = await getPortfolioById(portfolioId);

    if (!portfolio) {
      return NextResponse.json(
        { error: "Portofoliul nu a fost găsit" },
        { status: 404 }
      );
    }

    // Check if user owns this portfolio
    if (portfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să accesezi acest portofoliu" },
        { status: 403 }
      );
    }

    return NextResponse.json({
      portfolio,
      message: "Portofoliul a fost încărcat cu succes",
    });
  } catch (error) {
    console.error("Error fetching portfolio:", error);
    return NextResponse.json(
      { error: "Nu s-a putut încărca portofoliul" },
      { status: 500 }
    );
  }
}

// PUT /api/portfolios/[id] - Update a portfolio
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const { id } = await params;
    const portfolioId = id;

    if (!portfolioId) {
      return NextResponse.json(
        { error: "ID-ul portofoliului este obligatoriu" },
        { status: 400 }
      );
    }

    // Check if portfolio exists and user owns it
    const existingPortfolio = await getPortfolioById(portfolioId);

    if (!existingPortfolio) {
      return NextResponse.json(
        { error: "Portofoliul nu a fost găsit" },
        { status: 404 }
      );
    }

    if (existingPortfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să modifici acest portofoliu" },
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate request body
    const validationResult = portfolioEditSchema.safeParse(body);

    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => ({
        field: err.path.join("."),
        message: err.message,
      }));

      return NextResponse.json(
        {
          error: "Datele introduse nu sunt valide",
          details: errors,
        },
        { status: 400 }
      );
    }

    const { name, description, is_active } = validationResult.data;

    // Prepare updates object
    const updates: {
      name?: string;
      description?: string | null;
      is_active?: boolean;
    } = {};

    if (name !== undefined) {
      updates.name = name;
    }

    if (description !== undefined) {
      updates.description = description || null;
    }

    if (is_active !== undefined) {
      updates.is_active = is_active;
    }

    // Update the portfolio
    const updatedPortfolio = await updatePortfolio(portfolioId, updates);

    return NextResponse.json({
      portfolio: updatedPortfolio,
      message: "Portofoliul a fost actualizat cu succes",
    });
  } catch (error) {
    console.error("Error updating portfolio:", error);

    if (error instanceof Error) {
      if (error.message === "Portofoliul nu a fost găsit") {
        return NextResponse.json({ error: error.message }, { status: 404 });
      }
    }

    return NextResponse.json(
      { error: "Nu s-a putut actualiza portofoliul" },
      { status: 500 }
    );
  }
}

// DELETE /api/portfolios/[id] - Delete a portfolio
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const { id } = await params;
    const portfolioId = id;

    if (!portfolioId) {
      return NextResponse.json(
        { error: "ID-ul portofoliului este obligatoriu" },
        { status: 400 }
      );
    }

    const existingPortfolio = await getPortfolioById(portfolioId);

    if (!existingPortfolio) {
      return NextResponse.json(
        { error: "Portofoliul nu a fost găsit" },
        { status: 404 }
      );
    }

    if (existingPortfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să ștergi acest portofoliu" },
        { status: 403 }
      );
    }

    await deletePortfolio(portfolioId);

    return NextResponse.json({
      success: true,
      message: "Portofoliul a fost șters cu succes",
    });
  } catch (error) {
    console.error("Error deleting portfolio:", error);

    if (error instanceof Error) {
      if (error.message === "Portofoliul nu a fost găsit") {
        return NextResponse.json({ error: error.message }, { status: 404 });
      }
    }

    return NextResponse.json(
      { error: "Nu s-a putut șterge portofoliul" },
      { status: 500 }
    );
  }
}
