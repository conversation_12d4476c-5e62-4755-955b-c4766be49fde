"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { PieChart, Plus, TrendingUp } from "lucide-react";

interface EmptyPortfoliosStateProps {
  className?: string;
  onAddTransaction?: () => void;
}

export function EmptyPortfoliosState({
  className,
  onAddTransaction,
}: EmptyPortfoliosStateProps) {
  return (
    <div className={cn("flex items-center justify-center py-12", className)}>
      <Card className="max-w-md w-full">
        <CardContent className="pt-6">
          <div className="text-center space-y-6">
            {/* Icon */}
            <div className="mx-auto w-16 h-16 bg-portavio-blue/10 rounded-full flex items-center justify-center">
              <PieChart className="h-8 w-8 text-portavio-blue" />
            </div>

            {/* Content */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground">
                Niciun portofoliu activ
              </h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                Nu ai încă portofolii active. Creează primul tău portofoliu și
                începe să îți urmărești investițiile.
              </p>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              <Button asChild className="w-full gap-2">
                <Button onClick={onAddTransaction}>
                  <Plus className="h-4 w-4" />
                  Adaugă prima tranzacție
                </Button>
              </Button>

              <div className="text-xs text-muted-foreground">
                Un portofoliu va fi creat automat când adaugi prima tranzacție
              </div>
            </div>

            {/* Features Preview */}
            <div className="pt-4 border-t">
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-foreground">
                  Ce poți face cu portofoliile:
                </h4>
                <div className="space-y-2 text-xs text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-3 w-3 text-portavio-orange" />
                    <span>Urmărește performanța investițiilor</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <PieChart className="h-3 w-3 text-portavio-blue" />
                    <span>Vizualizează distribuția activelor</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Plus className="h-3 w-3 text-green-500" />
                    <span>Adaugă și gestionează tranzacții</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
