import { PortfolioDetailClient } from "@/components/portfolios/portfolio-detail-client";
import { auth } from "@/lib/auth";
import { getPortfolioById } from "@/utils/db/portfolio-queries";
import { Metadata } from "next";
import { headers } from "next/headers";
import { notFound, redirect } from "next/navigation";

interface PortfolioPageProps {
  params: Promise<{
    id: string;
  }>;
}

export const metadata: Metadata = {
  title: "Portofoliu - Portavio",
  description: "Detalii și tranzacții pentru portofoliul tău cu Portavio.",
  keywords: [
    "portofoliu",
    "tranzacții",
    "investiții",
    "urm<PERSON><PERSON><PERSON>",
    "performanță",
    "portavio",
  ],
  openGraph: {
    title: "Portofoliu - Portavio",
    description: "Detalii și tranzacții pentru portofoliul tău cu Portavio.",
    type: "website",
  },
};

export default async function PortfolioPage({ params }: PortfolioPageProps) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    redirect("/auth/signin");
  }

  const { id } = await params;
  let portfolio;

  try {
    portfolio = await getPortfolioById(id);
  } catch (error) {
    console.error("Error fetching portfolio:", error);
    notFound();
  }

  if (!portfolio) {
    notFound();
  }

  if (portfolio.user_id !== session.user.id) {
    redirect("/portfolios");
  }

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-7xl">
      <PortfolioDetailClient portfolio={portfolio} />
    </div>
  );
}
