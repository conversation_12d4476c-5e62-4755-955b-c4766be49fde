import { hasuraQuery } from "@/utils/db/hasura";
import { sendPasswordResetEmail } from "./sendEmail";

/**
 * Check if a user has credential-based authentication (email/password)
 * @param email User's email address
 * @returns Promise<boolean> - true if user has credential account, false otherwise
 */
async function hasCredentialAccount(email: string): Promise<boolean> {
  try {
    const query = `
      query CheckCredentialAccount($email: String!) {
        ptvuser_user(where: {email: {_eq: $email}}) {
          id
          accounts(where: {providerId: {_eq: "credential"}}) {
            id
            providerId
          }
        }
      }
    `;

    const result = await hasuraQuery<{
      ptvuser_user: Array<{
        id: string;
        accounts: Array<{
          id: string;
          providerId: string;
        }>;
      }>;
    }>(query, { variables: { email } });

    // Check if user exists and has at least one credential account
    const user = result.ptvuser_user?.[0];
    return user && user.accounts.length > 0;
  } catch (error) {
    console.error("Error checking credential account:", error);
    // For security, return false on error to prevent sending reset emails
    return false;
  }
}

/**
 * Handle password reset request with credential account validation
 * This function contains the business logic for determining whether to send
 * a password reset email based on the user's account type.
 * 
 * @param user User object from better-auth
 * @param url Reset URL with token
 * @param token Reset token
 */
export async function handlePasswordResetRequest(
  user: { email: string; name?: string | null },
  url: string,
  token: string
): Promise<void> {
  console.info("handlePasswordResetRequest:", {
    userEmail: user.email,
    url,
    token: token.substring(0, 10) + "...",
  });

  // Check if user has credential-based authentication
  const hasCredentials = await hasCredentialAccount(user.email);

  if (!hasCredentials) {
    console.warn(
      `Password reset blocked for ${user.email}: No credential account found`
    );
    // For security, we silently fail without revealing whether the email exists
    // or what type of account it is. This prevents enumeration attacks.
    return;
  }

  // Only send reset email if user has credential-based authentication
  console.info(`Sending password reset email to ${user.email}`);
  await sendPasswordResetEmail(user.email, user.name || "", token, url);
}
