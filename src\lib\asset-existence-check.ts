/**
 * Asset Existence Check Utilities
 * Provides functions to check if assets exist in the database
 */

import { getAssetByTicker } from "@/utils/db/asset-queries";
import { normalizeTicker } from "./ticker-utils";

/**
 * Check if a ticker exists in the database
 */
export async function checkAssetExists(ticker: string): Promise<boolean> {
  try {
    const normalizedTicker = normalizeTicker(ticker);
    const asset = await getAssetByTicker(normalizedTicker);
    return asset !== null;
  } catch (error) {
    console.error("Error checking asset existence:", error);
    return false;
  }
}

/**
 * Check multiple tickers for existence in the database
 * Returns a Map with ticker -> exists boolean
 */
export async function checkMultipleAssetsExist(tickers: string[]): Promise<Map<string, boolean>> {
  const results = new Map<string, boolean>();
  
  // Process tickers in parallel for better performance
  const promises = tickers.map(async (ticker) => {
    const normalizedTicker = normalizeTicker(ticker);
    const exists = await checkAssetExists(normalizedTicker);
    return { ticker: normalizedTicker, exists };
  });

  try {
    const resolvedResults = await Promise.all(promises);
    
    for (const { ticker, exists } of resolvedResults) {
      results.set(ticker, exists);
    }
  } catch (error) {
    console.error("Error checking multiple assets existence:", error);
    // Return empty map on error
  }

  return results;
}

/**
 * API endpoint to check if a ticker exists
 * Can be called from client-side components
 */
export async function checkAssetExistsAPI(ticker: string): Promise<boolean> {
  try {
    const normalizedTicker = normalizeTicker(ticker);
    const response = await fetch(`/api/assets/check-exists?ticker=${encodeURIComponent(normalizedTicker)}`);
    
    if (!response.ok) {
      console.error("Asset existence check API failed:", response.statusText);
      return false;
    }

    const data = await response.json();
    return data.exists === true;
  } catch (error) {
    console.error("Error calling asset existence check API:", error);
    return false;
  }
}

/**
 * Batch check multiple tickers via API
 */
export async function checkMultipleAssetsExistAPI(tickers: string[]): Promise<Map<string, boolean>> {
  const results = new Map<string, boolean>();
  
  try {
    const normalizedTickers = tickers.map(normalizeTicker);
    const response = await fetch('/api/assets/check-exists-batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ tickers: normalizedTickers }),
    });
    
    if (!response.ok) {
      console.error("Batch asset existence check API failed:", response.statusText);
      return results;
    }

    const data = await response.json();
    
    if (data.results && Array.isArray(data.results)) {
      for (const { ticker, exists } of data.results) {
        results.set(ticker, exists === true);
      }
    }
  } catch (error) {
    console.error("Error calling batch asset existence check API:", error);
  }

  return results;
}
