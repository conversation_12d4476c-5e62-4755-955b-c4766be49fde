"use client";

import { useAuth } from "@/hooks/use-auth";
import { getImageSourceType } from "@/lib/account-schemas";
import {
  useCallback,
  useEffect,
  useState,
  createContext,
  useContext,
  ReactNode,
} from "react";

interface UseProfilePictureReturn {
  profileImage: string | undefined;
  imageSourceType: "url" | "base64" | null;
  isLoading: boolean;
  refreshProfilePicture: () => void;
}

interface ProfilePictureContextType {
  profilePicture: string | null;
  isLoading: boolean;
  refreshProfilePicture: () => void;
}

const ProfilePictureContext = createContext<
  ProfilePictureContextType | undefined
>(undefined);

interface ProfilePictureProviderProps {
  children: ReactNode;
}

/**
 * Provider component that manages global profile picture state
 * This ensures all components using profile pictures stay in sync
 */
export function ProfilePictureProvider({
  children,
}: ProfilePictureProviderProps) {
  const { user, isAuthenticated } = useAuth();
  const [profilePicture, setProfilePicture] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchProfilePicture = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      setProfilePicture(null);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/account/data");
      const result = await response.json();

      if (
        response.ok &&
        result.success &&
        result.data?.profile_picture?.image_data
      ) {
        setProfilePicture(result.data.profile_picture.image_data);
      } else {
        setProfilePicture(null);
      }
    } catch (error) {
      console.error("Error fetching profile picture:", error);
      setProfilePicture(null);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user?.id]);

  useEffect(() => {
    fetchProfilePicture();
  }, [fetchProfilePicture]);

  const contextValue: ProfilePictureContextType = {
    profilePicture,
    isLoading,
    refreshProfilePicture: fetchProfilePicture,
  };

  return (
    <ProfilePictureContext.Provider value={contextValue}>
      {children}
    </ProfilePictureContext.Provider>
  );
}

/**
 * Custom hook to manage user profile pictures
 * Handles both base64 images from database and external URLs (Google, etc.)
 * Base64 images take priority over external URLs
 */
export function useProfilePicture(): UseProfilePictureReturn {
  const { user } = useAuth();
  const context = useContext(ProfilePictureContext);

  if (!context) {
    throw new Error(
      "useProfilePicture must be used within a ProfilePictureProvider"
    );
  }

  const { profilePicture, isLoading, refreshProfilePicture } = context;

  // Determine which image to use: custom profile picture (base64) takes priority
  const profileImage = profilePicture || user?.image || undefined;
  const imageSourceType = getImageSourceType(profileImage);

  return {
    profileImage,
    imageSourceType,
    isLoading,
    refreshProfilePicture,
  };
}
