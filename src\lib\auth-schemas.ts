import { z } from "zod";

// Sign Up Schema
export const signUpSchema = z
  .object({
    email: z
      .string()
      .min(1, "Email-ul este obligatoriu")
      .email("Email invalid"),
    name: z
      .string()
      .min(2, "Numele trebuie să aibă cel puțin 2 caractere")
      .max(100, "Numele nu poate avea mai mult de 100 de caractere")
      .regex(
        /^[a-zA-ZăâîșțĂÂÎȘȚ\s\-']+$/,
        "Numele poate conține doar litere, spații, cratimă și apostrof"
      ),
    username: z
      .string()
      .min(3, "Username-ul trebuie să aibă cel puțin 3 caractere")
      .max(30, "Username-ul nu poate avea mai mult de 30 de caractere")
      .regex(
        /^[a-zA-Z0-9_.]+$/,
        "Username-ul poate conține doar litere, cifre, puncte și underscore"
      )
      .refine(
        (username) => {
          const reservedUsernames = [
            "admin",
            "root",
            "system",
            "api",
            "www",
            "portavio",
          ];
          return !reservedUsernames.includes(username.toLowerCase());
        },
        {
          message: "Acest username nu este disponibil",
        }
      ),
    password: z
      .string()
      .min(8, "Parola trebuie să aibă cel puțin 8 caractere")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Parola trebuie să conțină cel puțin o literă mică, o literă mare și o cifră"
      ),
    confirmPassword: z.string(),
    acceptTerms: z.boolean().refine((val) => val === true, {
      message: "Trebuie să accepți Termenii și Condițiile",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Parolele nu se potrivesc",
    path: ["confirmPassword"],
  });

// Sign In Schema
export const signInSchema = z.object({
  identifier: z.string().min(1, "Email sau username este obligatoriu"),
  password: z.string().min(1, "Parola este obligatorie"),
});

// Types
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type SignInFormData = z.infer<typeof signInSchema>;
