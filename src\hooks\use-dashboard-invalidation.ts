"use client";

import { useQueryClient } from "@tanstack/react-query";
import { portfolioCompositionKeys } from "./use-portfolio-composition";
import { portfolioPerformanceKeys } from "./use-portfolio-performance";
import { portfolioMetricsKeys } from "./use-portfolio-metrics";
import { portfolioDividendsKeys } from "./use-portfolio-dividends";
import { companiesDataKeys } from "./use-companies-data";
import { portfoliosKeys } from "./use-portfolios-query";

/**
 * Centralized function to invalidate all dashboard-related queries
 * This ensures that any data mutation that affects portfolio/transaction data
 * will refresh all dashboard components automatically
 */
export function invalidateAllDashboardQueries(
  queryClient: ReturnType<typeof useQueryClient>
) {
  // Invalidate all dashboard query types
  queryClient.invalidateQueries({
    queryKey: portfolioCompositionKeys.all,
  });

  queryClient.invalidateQueries({
    queryKey: portfolioPerformanceKeys.all,
  });

  queryClient.invalidateQueries({
    queryKey: portfolioMetricsKeys.all,
  });

  queryClient.invalidateQueries({
    queryKey: portfolioDividendsKeys.all,
  });

  queryClient.invalidateQueries({
    queryKey: companiesDataKeys.all,
  });

  // Also invalidate portfolios queries as they might affect dashboard selectors
  queryClient.invalidateQueries({
    queryKey: portfoliosKeys.all,
  });
}

/**
 * Hook that provides dashboard invalidation functionality
 * Use this when you need to invalidate dashboard queries from components
 */
export function useDashboardInvalidation() {
  const queryClient = useQueryClient();

  return {
    invalidateAllDashboardQueries: () => invalidateAllDashboardQueries(queryClient),
  };
}
