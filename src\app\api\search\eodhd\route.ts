import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import {
  createEODHDSearchClient,
  EODHDSearchError,
  ProcessedSearchResult,
} from "@/lib/eodhd-search-client";

// Validation schema for search parameters
const searchParamsSchema = z.object({
  q: z
    .string()
    .min(1, "Termenul de căutare trebuie să aibă cel puțin 1 caracter")
    .max(50, "Termenul de căutare nu poate avea mai mult de 50 de caractere"),
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 25))
    .refine(
      (val) => val >= 1 && val <= 100,
      "Limita trebuie să fie între 1 și 100"
    ),
});

// Response types
interface EODHDSearchSuccessResponse {
  success: true;
  data: ProcessedSearchResult[];
  count: number;
  query: string;
  limit: number;
  source: "eodhd";
}

interface EODHDSearchErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
  source: "eodhd";
}

// GET /api/search/eodhd - Search for tickers using EODHD API
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    const limitParam = searchParams.get("limit");

    // Validate search parameters
    let validatedParams;
    try {
      validatedParams = searchParamsSchema.parse({
        q: query,
        limit: limitParam,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: "Parametri invalizi",
            code: "VALIDATION_ERROR",
            details: error.errors.map((err) => ({
              field: err.path.join("."),
              message: err.message,
            })),
            source: "eodhd",
          } as EODHDSearchErrorResponse,
          { status: 400 }
        );
      }
      throw error;
    }

    console.log(
      `EODHD search request: "${validatedParams.q}" (limit: ${validatedParams.limit})`
    );

    // Create EODHD client and search
    const eodhd = await createEODHDSearchClient();
    const searchResults = await eodhd.searchTickers(validatedParams.q);

    // Apply limit to results
    const limitedResults = searchResults.slice(0, validatedParams.limit);

    console.log(
      `EODHD search completed: ${limitedResults.length} results for "${validatedParams.q}"`
    );

    const successResponse: EODHDSearchSuccessResponse = {
      success: true,
      data: limitedResults,
      count: limitedResults.length,
      query: validatedParams.q,
      limit: validatedParams.limit,
      source: "eodhd",
    };

    return NextResponse.json(successResponse);
  } catch (error) {
    console.error("Error in EODHD search API:", error);

    // Handle EODHD-specific errors
    if (error instanceof EODHDSearchError) {
      let statusCode = 500;
      let errorCode = "EODHD_ERROR";

      if (error.statusCode === 401 || error.statusCode === 403) {
        statusCode = 401;
        errorCode = "AUTH_ERROR";
      } else if (error.statusCode === 429) {
        statusCode = 429;
        errorCode = "RATE_LIMITED";
      } else if (error.statusCode === 404) {
        statusCode = 404;
        errorCode = "NOT_FOUND";
      } else if (
        error.statusCode &&
        error.statusCode >= 400 &&
        error.statusCode < 500
      ) {
        statusCode = 400;
        errorCode = "CLIENT_ERROR";
      }

      return NextResponse.json(
        {
          success: false,
          error: error.message,
          code: errorCode,
          details: error.originalError,
          source: "eodhd",
        } as EODHDSearchErrorResponse,
        { status: statusCode }
      );
    }

    // Handle environment configuration errors
    if (error instanceof Error && error.message.includes("EODHD_API_TOKEN")) {
      return NextResponse.json(
        {
          success: false,
          error: "Serviciul de căutare nu este configurat corect",
          code: "CONFIG_ERROR",
          source: "eodhd",
        } as EODHDSearchErrorResponse,
        { status: 503 }
      );
    }

    // Handle generic errors
    return NextResponse.json(
      {
        success: false,
        error: "A apărut o eroare neașteptată la căutarea simbolurilor",
        code: "INTERNAL_ERROR",
        details: error instanceof Error ? error.message : String(error),
        source: "eodhd",
      } as EODHDSearchErrorResponse,
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește GET pentru căutare",
      code: "METHOD_NOT_ALLOWED",
      source: "eodhd",
    } as EODHDSearchErrorResponse,
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește GET pentru căutare",
      code: "METHOD_NOT_ALLOWED",
      source: "eodhd",
    } as EODHDSearchErrorResponse,
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește GET pentru căutare",
      code: "METHOD_NOT_ALLOWED",
      source: "eodhd",
    } as EODHDSearchErrorResponse,
    { status: 405 }
  );
}
