"use client";

import { useState, useEffect } from "react";
import { TransactionForm } from "@/components/transactions/transaction-form";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { TransactionFormData } from "@/lib/transaction-schemas";
import { Portfolio } from "@/utils/db/portfolio-queries";
import { useCreateTransaction } from "@/hooks/use-transactions-query";
import { AlertCircle, Loader2, TrendingUp } from "lucide-react";
import { toast } from "sonner";

interface AddTransactionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultPortfolioId?: string;
  onSuccess?: () => void;
}

export function AddTransactionModal({
  open,
  onOpenChange,
  defaultPortfolioId,
  onSuccess,
}: AddTransactionModalProps) {
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use TanStack Query mutation for creating transactions
  const createTransactionMutation = useCreateTransaction();

  useEffect(() => {
    if (open) {
      loadPortfolios();
    }
  }, [open]);

  const loadPortfolios = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // First, ensure user has at least one portfolio
      const ensureResponse = await fetch("/api/portfolios/ensure", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!ensureResponse.ok) {
        throw new Error("Nu s-au putut încărca portofoliile");
      }

      const ensureData = await ensureResponse.json();
      setPortfolios(ensureData.portfolios);

      if (ensureData.defaultCreated) {
        toast.success("A fost creat un portofoliu implicit pentru tine!");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      setError(errorMessage);
      console.error("Error loading portfolios:", errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (data: TransactionFormData) => {
    createTransactionMutation.mutate(data, {
      onSuccess: () => {
        toast.success("Tranzacția a fost adăugată cu succes!");
        onOpenChange(false);
        onSuccess?.();
      },
      onError: (error) => {
        const errorMessage =
          error instanceof Error ? error.message : "A apărut o eroare";
        toast.error(errorMessage);
      },
    });
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setError(null);
      setIsLoading(true);
    }
    onOpenChange(newOpen);
  };

  // Determine the portfolio ID to use
  const portfolioIdToUse = defaultPortfolioId || portfolios[0]?.id;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold flex items-center gap-2">
            <TrendingUp className="h-6 w-6 text-portavio-orange" />
            Adaugă Tranzacție
          </DialogTitle>
          <DialogDescription>
            Completează formularul pentru a adăuga o nouă tranzacție în
            portofoliul tău
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex flex-col items-center gap-4">
              <Loader2 className="h-8 w-8 animate-spin text-portavio-orange" />
              <p className="text-muted-foreground">Se încarcă...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center gap-4 text-center py-8">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <div>
              <h3 className="text-lg font-semibold mb-2">A apărut o eroare</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <button
                onClick={loadPortfolios}
                className="text-sm text-portavio-orange hover:underline"
              >
                Încearcă din nou
              </button>
            </div>
          </div>
        ) : (
          <div className="mt-4">
            <TransactionForm
              portfolios={portfolios}
              onSubmit={handleSubmit}
              isLoading={createTransactionMutation.isPending}
              defaultPortfolioId={portfolioIdToUse}
            />
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
