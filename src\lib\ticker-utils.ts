/**
 * Ticker validation and utility functions
 */

/**
 * Validates if a string is a valid ticker format
 * Supports formats like: AAPL, AAPL.US, MSFT.US, H2O.RO, SNP.RO, BTC-USD, etc.
 */
export function isValidTickerFormat(ticker: string): boolean {
  if (!ticker || typeof ticker !== 'string') {
    return false;
  }

  const trimmed = ticker.trim().toUpperCase();
  
  // Must be between 1 and 20 characters
  if (trimmed.length < 1 || trimmed.length > 20) {
    return false;
  }

  // Valid ticker pattern:
  // - Starts with a letter or number
  // - Can contain letters, numbers, dots, hyphens
  // - Can have exchange suffix after dot (e.g., .US, .RO, .L)
  // - Can have currency pair separator with hyphen (e.g., BTC-USD)
  const tickerPattern = /^[A-Z0-9][A-Z0-9.-]*[A-Z0-9]$|^[A-Z0-9]$/;
  
  if (!tickerPattern.test(trimmed)) {
    return false;
  }

  // Additional validation rules:
  // - Cannot start or end with dot or hyphen
  // - Cannot have consecutive dots or hyphens
  // - Cannot have both dots and hyphens (choose one format)
  if (trimmed.startsWith('.') || trimmed.startsWith('-') ||
      trimmed.endsWith('.') || trimmed.endsWith('-')) {
    return false;
  }

  if (trimmed.includes('..') || trimmed.includes('--') || 
      trimmed.includes('.-') || trimmed.includes('-.')) {
    return false;
  }

  // If it contains both dots and hyphens, it's invalid
  if (trimmed.includes('.') && trimmed.includes('-')) {
    return false;
  }

  return true;
}

/**
 * Normalizes a ticker string to uppercase and trims whitespace
 */
export function normalizeTicker(ticker: string): string {
  return ticker.trim().toUpperCase();
}

/**
 * Extracts the base symbol from a ticker (removes exchange suffix)
 * Example: "AAPL.US" -> "AAPL", "BTC-USD" -> "BTC"
 */
export function getBaseSymbol(ticker: string): string {
  const normalized = normalizeTicker(ticker);
  
  // Remove exchange suffix after dot
  if (normalized.includes('.')) {
    return normalized.split('.')[0];
  }
  
  // Remove currency pair suffix after hyphen
  if (normalized.includes('-')) {
    return normalized.split('-')[0];
  }
  
  return normalized;
}

/**
 * Gets the exchange suffix from a ticker
 * Example: "AAPL.US" -> "US", "H2O.RO" -> "RO"
 */
export function getExchangeSuffix(ticker: string): string | null {
  const normalized = normalizeTicker(ticker);
  
  if (normalized.includes('.')) {
    const parts = normalized.split('.');
    return parts.length > 1 ? parts[1] : null;
  }
  
  return null;
}

/**
 * Common ticker format examples for validation messages
 */
export const TICKER_FORMAT_EXAMPLES = [
  'AAPL',
  'AAPL.US', 
  'MSFT.US',
  'H2O.RO',
  'SNP.RO',
  'BTC-USD',
  'ETH-EUR'
] as const;

/**
 * Error messages in Romanian for ticker validation
 */
export const TICKER_VALIDATION_MESSAGES = {
  INVALID_FORMAT: 'Formatul simbolului nu este valid',
  TOO_SHORT: 'Simbolul trebuie să aibă cel puțin 1 caracter',
  TOO_LONG: 'Simbolul nu poate avea mai mult de 20 de caractere',
  INVALID_CHARACTERS: 'Simbolul poate conține doar litere, cifre, puncte și liniuțe',
  EXAMPLES: `Exemple valide: ${TICKER_FORMAT_EXAMPLES.join(', ')}`,
} as const;
