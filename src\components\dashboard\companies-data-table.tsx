"use client";

import { useMemo, useState, useCallback } from "react";
import { Building2, Loader2, Edit } from "lucide-react";

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { useCompaniesData } from "@/hooks/use-companies-data";
import { useAuth } from "@/hooks/use-auth";
import { useCompaniesTablePreferences } from "@/hooks/use-companies-table-preferences";
import { createCompaniesTableColumns } from "./companies-table-columns";
import { CompaniesTableColumnCustomization } from "./companies-table-column-customization";
import { SupportedCurrency } from "./currency-selector";

interface CompaniesDataTableProps {
  selectedPortfolios: string[];
  displayCurrency: SupportedCurrency;
}

export function CompaniesDataTable({
  selectedPortfolios,
  displayCurrency,
}: CompaniesDataTableProps) {
  const [isCustomizationOpen, setIsCustomizationOpen] = useState(false);
  const { user } = useAuth();

  const {
    data: companiesData,
    isLoading,
    error,
  } = useCompaniesData(selectedPortfolios, displayCurrency);

  // Create base columns
  const baseColumns = useMemo(
    () => createCompaniesTableColumns(displayCurrency),
    [displayCurrency]
  );

  // Extract column information for preferences
  const columnInfo = useMemo(() => {
    // Map of column IDs to their display titles
    const columnTitles: Record<string, string> = {
      logo_url: "Logo",
      company: "Companie/Ticker",
      shares: "Acțiuni",
      currentPrice: "Preț curent",
      avgCost: "DCA (Preț mediu)",
      dcaPercentage: "% DCA",
      fiftyTwoWeekLowConverted: "52W Min",
      fiftyTwoWeekHighConverted: "52W Max",
      dividendYield: "Div Yield",
      costValue: "Valoare cost",
      marketValue: "Valoare piață",
      dollarReturn: "$ Return",
      percentReturn: "% Return",
      yesterdayChange: "Ieri",
      allocation: "Alocare",
      sector: "Sector",
      industry: "Industrie",
      country: "Țară",
      dividendIncome: "Dividende",
      dividendPayments: "Nr. Plăți",
      totalTransactionFees: "Comisioane",
    };

    return baseColumns
      .map((col) => {
        const id = (col as any).accessorKey || col.id || "";
        return {
          id,
          title: columnTitles[id] || id,
          canHide: col.enableHiding !== false,
        };
      })
      .filter((col) => col.id);
  }, [baseColumns]);

  // Get default column order and visibility
  const defaultColumnOrder = useMemo(
    () => columnInfo.map((col) => col.id),
    [columnInfo]
  );
  const defaultColumnVisibility = useMemo(() => {
    const visibility: Record<string, boolean> = {};
    columnInfo.forEach((col) => {
      visibility[col.id] = true; // All columns visible by default
    });
    return visibility;
  }, [columnInfo]);

  // Use preferences hook
  const { columnVisibility, columnOrder, setColumnVisibility, setColumnOrder } =
    useCompaniesTablePreferences({
      userId: user?.id || "",
      defaultColumnOrder,
      defaultColumnVisibility,
    });

  // Reorder columns based on saved preferences
  const orderedColumns = useMemo(() => {
    if (!columnOrder.length) return baseColumns;

    const columnMap = new Map(
      baseColumns.map((col) => [(col as any).accessorKey || col.id, col])
    );
    const ordered = columnOrder
      .map((id) => columnMap.get(id))
      .filter(Boolean) as typeof baseColumns;

    // Add any columns that aren't in the order (new columns)
    baseColumns.forEach((col) => {
      const id = (col as any).accessorKey || col.id;
      if (!columnOrder.includes(id)) {
        ordered.push(col);
      }
    });

    return ordered;
  }, [baseColumns, columnOrder]);

  // Handle column customization save
  const handleColumnCustomizationSave = useCallback(
    (visibility: Record<string, boolean>, order: string[]) => {
      setColumnVisibility(visibility);
      setColumnOrder(order);
    },
    [setColumnVisibility, setColumnOrder]
  );

  // Wrapper for TanStack Table's OnChangeFn type
  const handleColumnVisibilityChange = useCallback(
    (updaterOrValue: any) => {
      if (typeof updaterOrValue === "function") {
        setColumnVisibility(updaterOrValue(columnVisibility));
      } else {
        setColumnVisibility(updaterOrValue);
      }
    },
    [columnVisibility, setColumnVisibility]
  );

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-portavio-orange" />
            <span>Companiile mele</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Se încarcă datele companiilor...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-portavio-orange" />
            <span>Companiile mele</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <div className="text-lg font-medium mb-2">
                Eroare la încărcarea datelor
              </div>
              <div className="text-sm">
                {error instanceof Error
                  ? error.message
                  : "A apărut o eroare neașteptată"}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!companiesData || companiesData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-portavio-orange" />
            <span>Companiile mele</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <div className="text-lg font-medium mb-2">
                Nu există date disponibile
              </div>
              <div className="text-sm">
                {selectedPortfolios.length === 0
                  ? "Selectați un portofoliu pentru a vedea companiile"
                  : "Nu există tranzacții în portofoliile selectate"}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Building2 className="h-6 w-6 text-portavio-orange" />
            <div>
              <span>Companiile mele</span>
              <p className="text-lg text-muted-foreground font-normal">
                ({companiesData.length} companii)
              </p>
            </div>
          </div>
          <Button
            variant="outlineLightBlue"
            size="lg"
            className="h-8 w-8 p-0 rounded-full touch-manipulation"
            onClick={() => setIsCustomizationOpen(true)}
            title="Personalizează coloanele"
          >
            <Edit className="h-4 w-4 text-black" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="px-6 pb-6">
          <DataTable
            columns={orderedColumns}
            data={companiesData}
            searchPlaceholder="Caută companii..."
            isLoading={isLoading}
            externalColumnVisibility={columnVisibility}
            onColumnVisibilityChange={handleColumnVisibilityChange}
          />
        </div>
      </CardContent>

      <CompaniesTableColumnCustomization
        open={isCustomizationOpen}
        onOpenChange={setIsCustomizationOpen}
        columns={columnInfo}
        columnVisibility={columnVisibility}
        columnOrder={columnOrder}
        onSave={handleColumnCustomizationSave}
      />
    </Card>
  );
}
