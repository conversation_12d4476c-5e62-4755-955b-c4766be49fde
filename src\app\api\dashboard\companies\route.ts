import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { getCompaniesData } from "@/utils/db/dashboard-queries";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu sunteți autentificat" },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const portfolioIdsParam = searchParams.get("portfolioIds");
    const displayCurrency = (searchParams.get("displayCurrency") || "EUR") as SupportedCurrency;

    if (!portfolioIdsParam) {
      return NextResponse.json(
        { error: "Portfolio IDs sunt necesare" },
        { status: 400 }
      );
    }

    // Parse portfolio IDs
    const portfolioIds = portfolioIdsParam.split(",").filter(Boolean);

    if (portfolioIds.length === 0) {
      return NextResponse.json(
        { error: "Cel puțin un portfolio ID este necesar" },
        { status: 400 }
      );
    }

    // Fetch companies data
    const companiesData = await getCompaniesData(portfolioIds, displayCurrency);

    return NextResponse.json({
      success: true,
      data: companiesData,
      message: "Datele companiilor au fost încărcate cu succes",
    });

  } catch (error) {
    console.error("Error in companies API:", error);
    
    return NextResponse.json(
      {
        error: error instanceof Error 
          ? error.message 
          : "Nu s-au putut încărca datele companiilor",
      },
      { status: 500 }
    );
  }
}
