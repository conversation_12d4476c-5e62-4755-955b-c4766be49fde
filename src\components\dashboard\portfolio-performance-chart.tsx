"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { usePortfolioPerformance } from "@/hooks/use-portfolio-performance";
import { TimePeriod } from "@/utils/db/dashboard-queries";
import { SupportedCurrency } from "./currency-selector";
import { Loader2, TrendingUp, TrendingDown, Info } from "lucide-react";
import { useState } from "react";
import { LineChart } from "../ui/line-chart";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

interface PortfolioPerformanceChartProps {
  selectedPortfolios: string[];
  displayCurrency: SupportedCurrency;
}

const timePeriodLabels: Record<TimePeriod, string> = {
  "1W": "1 Săptămână",
  "1M": "1 Lună",
  YTD: "De la început de an",
  "1Y": "1 An",
  MAX: "Maxim",
};

const formatCurrency = (value: number, currency: SupportedCurrency): string => {
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const formatPercentage = (value: number): string => {
  const sign = value >= 0 ? "+" : "";
  return `${sign}${value.toFixed(2)}%`;
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString("ro-RO", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
};

export function PortfolioPerformanceChart({
  selectedPortfolios,
  displayCurrency,
}: PortfolioPerformanceChartProps) {
  const [activeTimePeriod, setActiveTimePeriod] = useState<TimePeriod>("1W");
  const {
    data: performanceData,
    isLoading,
    error,
  } = usePortfolioPerformance(
    selectedPortfolios,
    activeTimePeriod,
    displayCurrency
  );

  if (isLoading) {
    return (
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>
              Performanța{" "}
              {selectedPortfolios.length === 1
                ? "Portofoliului"
                : "Portofoliilor"}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Se încarcă datele...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>
              Performanța{" "}
              {selectedPortfolios.length === 1
                ? "Portofoliului"
                : "Portofoliilor"}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p className="text-red-500 mb-2">Eroare la încărcarea datelor</p>
              <p className="text-sm">
                {error instanceof Error ? error.message : "Eroare necunoscută"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!performanceData || performanceData.data.length === 0) {
    return (
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>
              Performanța{" "}
              {selectedPortfolios.length === 1
                ? "Portofoliului"
                : "Portofoliilor"}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p>Nu există date de performanță disponibile</p>
              <p className="text-sm mt-2">
                Selectează portofolii cu tranzacții pentru a vedea performanța
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Prepare chart data
  const chartData = performanceData.data.map((point) => ({
    date: formatDate(point.date),
    "Valoare Portofoliu": point.value,
  }));

  const valueFormatter = (value: number) =>
    formatCurrency(value, displayCurrency);

  // Y-axis tick formatter that shows rounded integer values
  const yAxisTickFormatter = (value: number) => {
    const roundedValue = Math.round(value);
    return formatCurrency(roundedValue, displayCurrency);
  };

  // Calculate dynamic Y-axis range for better visualization
  const portfolioValues = performanceData.data.map((point) => point.value);
  const minPortfolioValue = Math.min(...portfolioValues);
  const maxPortfolioValue = Math.max(...portfolioValues);
  const valueRange = maxPortfolioValue - minPortfolioValue;

  // Calculate Y-axis bounds with intelligent scaling
  let yAxisMin: number;
  let yAxisMax: number;

  if (valueRange === 0) {
    // Handle case where all values are the same
    const baseValue = minPortfolioValue;
    const padding = Math.max(baseValue * 0.05, 100); // 5% padding or minimum 100 units
    yAxisMin = Math.max(0, baseValue - padding);
    yAxisMax = baseValue + padding;
  } else {
    // Calculate buffer as a percentage of the range
    const bufferPercentage = valueRange < maxPortfolioValue * 0.1 ? 0.2 : 0.1; // Larger buffer for small ranges
    const buffer = Math.max(
      valueRange * bufferPercentage,
      maxPortfolioValue * 0.02 // Minimum 2% of max value
    );

    yAxisMin = Math.max(0, minPortfolioValue - buffer); // Don't go below 0
    yAxisMax = maxPortfolioValue + buffer;

    // For very small fluctuations relative to the portfolio value, ensure minimum visible range
    const minVisibleRange = maxPortfolioValue * 0.05; // 5% of portfolio value
    if (yAxisMax - yAxisMin < minVisibleRange) {
      const center = (yAxisMin + yAxisMax) / 2;
      const halfRange = minVisibleRange / 2;
      yAxisMin = Math.max(0, center - halfRange);
      yAxisMax = center + halfRange;
    }
  }

  const isProfit = performanceData.totalProfitLoss >= 0;
  const profitLossColor = isProfit ? "text-green-600" : "text-red-600";
  const TrendIcon = isProfit ? TrendingUp : TrendingDown;

  return (
    <Card className="lg:col-span-2">
      <CardHeader className="pb-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex items-center justify-between flex-wrap gap-1">
            <CardTitle className="text-xl flex items-center gap-2 flex-wrap">
              Performanța{" "}
              {selectedPortfolios.length === 1
                ? "Portofoliului"
                : "Portofoliilor"}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 inline-block ml-1" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-[200px]">
                    Graficul pornește de la valoarea totală investită și
                    reflectă doar mișcările reale din tranzacțiile tale.
                  </p>
                </TooltipContent>
              </Tooltip>
            </CardTitle>
            <Badge
              variant="secondary"
              className="text-xl text-portavio-blue lg:hidden"
            >
              {formatCurrency(performanceData.currentValue, displayCurrency)}
            </Badge>
          </div>

          <div className="flex flex-wrap gap-1">
            {(Object.keys(timePeriodLabels) as TimePeriod[]).map((period) => (
              <Button
                key={period}
                variant={activeTimePeriod === period ? "default" : "outline"}
                size="sm"
                className="text-xs h-7"
                onClick={() => setActiveTimePeriod(period)}
              >
                {timePeriodLabels[period]}
              </Button>
            ))}
          </div>
        </div>

        {/* Performance Summary */}
        <div className="flex flex-col lg:flex-row lg:items-center gap-4 mt-4">
          <div className="hidden lg:block">
            <Badge
              variant="secondary"
              className="text-xl font-bold text-portavio-blue"
              title="Valoarea totală a portofoliului"
            >
              {formatCurrency(performanceData.currentValue, displayCurrency)}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <div
              className={`flex items-center gap-1 text-lg ${profitLossColor}`}
            >
              <TrendIcon className="h-4 w-4" />
              <span className="font-semibold text-lg">
                {formatPercentage(performanceData.totalProfitLossPercentage)}
              </span>
              <span className="text-lg font-semibold">
                (
                {formatCurrency(
                  performanceData.totalProfitLoss,
                  displayCurrency
                )}
                )
              </span>
            </div>
            <div className="text-xs text-muted-foreground">
              {timePeriodLabels[activeTimePeriod]}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="h-64">
          <LineChart
            data={chartData}
            index="date"
            categories={["Valoare Portofoliu"]}
            colors={["portavio-blue"]}
            valueFormatter={valueFormatter}
            yAxisTickFormatter={yAxisTickFormatter}
            showLegend={false}
            showGridLines={true}
            showXAxis={true}
            showYAxis={true}
            className="h-full"
            autoMinValue={false}
            minValue={yAxisMin}
            maxValue={yAxisMax}
            connectNulls={false}
          />
        </div>
      </CardContent>
    </Card>
  );
}
