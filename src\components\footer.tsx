import fbIcon from "@/app/assets/facebook.svg";
import instagramIcon from "@/app/assets/instagram.svg";
import linkedinIcon from "@/app/assets/linkedin.svg";
import logo from "@/app/assets/portavio-logo.svg";
import xIcon from "@/app/assets/x.svg";
import { BuildInfo } from "@/components/build-info";
import Image from "next/image";
import Link from "next/link";

export function Footer() {
  return (
    <footer className="bg-[#042D5F] text-white px-6 py-12 w-full relative">
      <div className="flex justify-center gap-24 w-full flex-wrap">
        <div className="flex flex-col items-end gap-2">
          <Link href={"/"}>
            <Image src={logo} alt="Portavio Logo" height={72} />
          </Link>

          <div className="flex flex-row items-center gap-4 px-3">
            <Link href={"/"}>
              <Image src={fbIcon} alt="Facebook" height={24} />
            </Link>
            <Link href={"/"}>
              <Image src={xIcon} alt="X" height={24} />
            </Link>
            <Link href={"/"}>
              <Image src={linkedinIcon} alt="LinkedIn" height={24} />
            </Link>
            <Link href={"/"}>
              <Image src={instagramIcon} alt="Instagram" height={24} />
            </Link>
          </div>
        </div>
        <div className="flex flex-col flex-wrap gap-2 text-lg text-white font-semibold">
          <Link
            href="/cookies-policy"
            className="hover:text-white transition-colors"
          >
            Politica cookies
          </Link>
          <Link
            href="/confidentiality"
            className="hover:text-white transition-colors"
          >
            Politica de confidențialitate
          </Link>
          <Link
            href="https://anpc.ro/"
            target="_blank"
            className="hover:text-white transition-colors"
          >
            Soluționarea online a litigiilor
          </Link>
          <Link href="/support" className="hover:text-white transition-colors">
            Suport
          </Link>
          <Link href="/contact" className="hover:text-white transition-colors">
            Contact
          </Link>
        </div>
        <div className="text-[12px] leading-relaxed max-w-[670px]">
          <p className="mb-4">
            Orice informație prezentată pe acest site este pur informativă și
            reprezintă doar părerile și convingerile noastre, prin urmare nu
            este o recomandare sau un sfat financiar. Nu trebuie să vă bazați pe
            nicio recomandare și/sau informație conținută pe acest site și
            înainte de a lua orice decizie de investiție, recomandăm să luați în
            considerare dacă aceasta este adecvată pentru situația dumneavoastră
            și să solicitați sfaturi financiare, fiscale și legale
            corespunzătoare.
          </p>
          <p>© 2025 Portavio</p>
        </div>
      </div>

      <div className="absolute bottom-4 right-6">
        <BuildInfo className="text-portavio-navy" />
      </div>
    </footer>
  );
}
