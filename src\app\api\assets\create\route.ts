import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { z } from "zod";
import {
  createAsset,
  AssetCreationResponse,
} from "@/lib/asset-creation-service";
import { isValidTickerFormat, normalizeTicker } from "@/lib/ticker-utils";

// Request validation schema
const createAssetRequestSchema = z.object({
  ticker: z
    .string()
    .min(1, "Simbolul este obligatoriu")
    .max(20, "Simbolul nu poate avea mai mult de 20 de caractere")
    .refine(
      (ticker) => isValidTickerFormat(ticker),
      "Formatul simbolului nu este valid"
    ),
  options: z
    .object({
      refresh: z.boolean().optional(),
      useMock: z.boolean().optional(),
      daysHistory: z.number().min(1).max(3650).optional(),
      yearsHistoryDividend: z.number().min(1).max(20).optional(),
      providers: z.array(z.string()).optional(),
    })
    .optional(),
});

type CreateAssetRequest = z.infer<typeof createAssetRequestSchema>;

// Response types
interface CreateAssetSuccessResponse {
  success: true;
  data: {
    asset: {
      asset_id: number;
      ticker: string;
      name: string;
      company?: string;
      logo_url?: string;
      currency?: {
        code: string;
        name?: string;
        symbol?: string;
      };
      exchange?: {
        code: string;
        name?: string;
      };
      sector?: {
        name: string;
      };
      industry?: {
        name: string;
      };
      country?: {
        code: string;
        name: string;
      };
      asset_type?: {
        name: string;
      };
    };
    hasHistory: boolean;
    hasDividends: boolean;
    latestPrice?: number;
    currency?: string;
  };
  message: string;
}

interface CreateAssetErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

// POST /api/assets/create - Create a new asset
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          error: "Nu ești autentificat",
          code: "UNAUTHORIZED",
        } as CreateAssetErrorResponse,
        { status: 401 }
      );
    }

    // Parse and validate request body
    let requestData: CreateAssetRequest;
    try {
      const body = await request.json();
      requestData = createAssetRequestSchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: "Datele cererii nu sunt valide",
            code: "VALIDATION_ERROR",
            details: error.errors.map((err) => ({
              field: err.path.join("."),
              message: err.message,
            })),
          } as CreateAssetErrorResponse,
          { status: 400 }
        );
      }
      throw error;
    }

    const { ticker, options = {} } = requestData;
    const normalizedTicker = normalizeTicker(ticker);

    console.log(
      `Asset creation request for ticker: ${normalizedTicker} by user: ${session.user.id}`
    );

    // Create the asset using the service
    const result: AssetCreationResponse = await createAsset(normalizedTicker, {
      refresh: true, // Always refresh when creating new assets
      useMock: options.useMock || false,
      daysHistory: options.daysHistory || 30,
      yearsHistoryDividend: options.yearsHistoryDividend || 10,
      providers: options.providers,
    });

    if (!result.success) {
      console.error(`Asset creation failed for ${normalizedTicker}:`, result);

      // Map error codes to appropriate HTTP status codes
      let statusCode = 500;
      if (result.code === "TICKER_NOT_FOUND") {
        statusCode = 404;
      } else if (
        result.code === "INVALID_TICKER_FORMAT" ||
        result.code === "VALIDATION_ERROR"
      ) {
        statusCode = 400;
      } else if (result.code === "AUTH_ERROR") {
        statusCode = 401;
      } else if (result.code === "RATE_LIMITED") {
        statusCode = 429;
      } else if (result.code === "SERVICE_UNAVAILABLE") {
        statusCode = 503;
      }

      return NextResponse.json(
        {
          success: false,
          error: result.error,
          code: result.code,
          details: result.details,
        } as CreateAssetErrorResponse,
        { status: statusCode }
      );
    }

    console.log(`Asset creation successful for ${normalizedTicker}:`, {
      asset_id: result.asset.asset_id,
      hasHistory: result.hasHistory,
      hasDividends: result.hasDividends,
      latestPrice: result.latestPrice,
    });

    const successResponse: CreateAssetSuccessResponse = {
      success: true,
      data: {
        asset: {
          asset_id: result.asset.asset_id,
          ticker: result.asset.ticker,
          name: result.asset.name,
          company: result.asset.company,
          logo_url: result.asset.logo_url,
          currency: result.asset.currency,
          exchange: result.asset.exchange,
          sector: result.asset.sector,
          industry: result.asset.industry,
          country: result.asset.country,
          asset_type: result.asset.asset_type,
        },
        hasHistory: result.hasHistory,
        hasDividends: result.hasDividends,
        latestPrice: result.latestPrice,
        currency: result.currency,
      },
      message: `Activul ${normalizedTicker} a fost creat cu succes`,
    };

    return NextResponse.json(successResponse, { status: 201 });
  } catch (error) {
    console.error("Unexpected error in asset creation API:", error);

    return NextResponse.json(
      {
        success: false,
        error: "A apărut o eroare neașteptată la crearea activului",
        code: "INTERNAL_ERROR",
        details: error instanceof Error ? error.message : String(error),
      } as CreateAssetErrorResponse,
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește POST pentru a crea un activ",
      code: "METHOD_NOT_ALLOWED",
    } as CreateAssetErrorResponse,
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește POST pentru a crea un activ",
      code: "METHOD_NOT_ALLOWED",
    } as CreateAssetErrorResponse,
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește POST pentru a crea un activ",
      code: "METHOD_NOT_ALLOWED",
    } as CreateAssetErrorResponse,
    { status: 405 }
  );
}
