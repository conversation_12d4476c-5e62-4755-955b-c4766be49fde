/**
 * External API client for Portavio Financial API
 * Handles authenticated requests to the Python API endpoints
 */

// API Response Types based on OpenAPI specification
export interface AssetResponse {
  asset_id: number;
  ticker: string;
  name: string;
  company?: string;
  logo_url?: string;
  isin?: string;
  currency?: {
    currency_id: number;
    code: string;
    name?: string;
    symbol?: string;
  };
  exchange?: {
    exchange_id: number;
    code: string;
    name?: string;
  };
  sector?: {
    sector_id: number;
    name: string;
  };
  industry?: {
    industry_id: number;
    name: string;
  };
  country?: {
    country_id: number;
    code: string;
    name: string;
  };
  asset_type?: {
    asset_type_id: number;
    name: string;
  };
  prices?: PriceResponse[];
}

export interface PriceResponse {
  price_id: number;
  date: string;
  open_price: number;
  high_price: number;
  low_price: number;
  close_price: number;
  adj_close: number;
  volume: number;
}

export interface MetricsResponse {
  metrics_id: number;
  date: string;
  pe_ratio?: number;
  forward_pe?: number;
  market_cap?: number;
  dividend_yield?: number;
}

export interface DividendResponse {
  dividend_id: number;
  ex_date: string;
  payment_date?: string;
  amount_per_share: number;
  currency?: {
    currency_id: number;
    code: string;
    name?: string;
    symbol?: string;
  };
  status: string;
  dividend_type?: string;
  frequency?: string;
  yield_percent?: number;
}

export interface AssetWithMetricsResponse {
  asset: AssetResponse;
  latest_metrics?: MetricsResponse;
}

export interface AssetWithDividendsResponse {
  asset: AssetResponse;
  dividends: DividendResponse[];
}

// API Error Types
export interface APIValidationError {
  loc: (string | number)[];
  msg: string;
  type: string;
}

export interface APIErrorResponse {
  detail?: APIValidationError[];
}

// Custom Error Classes
export class ExternalAPIError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = "ExternalAPIError";
  }
}

export class ExternalAPIValidationError extends ExternalAPIError {
  constructor(
    message: string,
    public validationErrors: APIValidationError[],
    statusCode?: number
  ) {
    super(message, statusCode);
    this.name = "ExternalAPIValidationError";
  }
}

// API Client Configuration
interface ExternalAPIClientConfig {
  baseURL: string;
  apiKey: string;
  timeout?: number;
}

/**
 * External API Client for Portavio Financial API
 */
export class ExternalAPIClient {
  private config: ExternalAPIClientConfig;

  constructor(config: ExternalAPIClientConfig) {
    this.config = {
      timeout: 30000, // 30 seconds default
      ...config,
    };
  }

  /**
   * Make an authenticated request to the external API
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, this.config.timeout);

    try {
      const url = `${this.config.baseURL}${endpoint}`;

      const response = await fetch(url, {
        ...options,
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": this.config.apiKey,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        let errorResponse: any = null;

        try {
          errorResponse = await response.json();

          if (response.status === 422 && errorResponse.detail) {
            // Validation error
            const validationErrors =
              errorResponse.detail as APIValidationError[];
            const errorMessages = validationErrors
              .map((err) => err.msg)
              .join(", ");
            throw new ExternalAPIValidationError(
              `Eroare de validare: ${errorMessages}`,
              validationErrors,
              response.status
            );
          } else if (errorResponse.error) {
            errorMessage = errorResponse.error;
          }
        } catch (parseError) {
          // If we can't parse the error response, use the status text
          console.warn("Failed to parse error response:", parseError);
          // errorMessage already set to default HTTP status message above
        }

        // Map common HTTP status codes to Romanian messages
        switch (response.status) {
          case 401:
            errorMessage = "Acces neautorizat la API";
            break;
          case 403:
            errorMessage = "Acces interzis la API";
            break;
          case 404:
            errorMessage = "Simbolul nu a fost găsit";
            break;
          case 429:
            errorMessage = "Prea multe cereri. Încearcă din nou mai târziu";
            break;
          case 500:
            errorMessage = "Eroare internă a serverului API";
            break;
          case 503:
            errorMessage = "Serviciul API este temporar indisponibil";
            break;
        }

        throw new ExternalAPIError(
          errorMessage,
          response.status,
          errorResponse
        );
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof ExternalAPIError) {
        throw error;
      }

      if (error instanceof TypeError && error.message.includes("fetch")) {
        throw new ExternalAPIError(
          "Eroare de rețea: Nu se poate conecta la API"
        );
      }

      if (error instanceof Error && error.name === "AbortError") {
        throw new ExternalAPIError(
          `Timeout: Cererea a expirat după ${this.config.timeout}ms`
        );
      }

      throw new ExternalAPIError(
        error instanceof Error ? error.message : "Eroare necunoscută"
      );
    }
  }

  /**
   * Get asset data from /api/assets/{ticker}/data
   */
  async getAssetData(
    ticker: string,
    options: {
      refresh?: boolean;
      useMock?: boolean;
      providers?: string[];
    } = {}
  ): Promise<AssetResponse> {
    const params = new URLSearchParams();

    if (options.refresh) params.append("refresh", "true");
    if (options.useMock) params.append("use_mock", "true");
    if (options.providers?.length) {
      options.providers.forEach((provider) =>
        params.append("providers", provider)
      );
    }

    const queryString = params.toString();
    const endpoint = `/api/assets/${encodeURIComponent(ticker)}/data${
      queryString ? `?${queryString}` : ""
    }`;

    return this.makeRequest<AssetResponse>(endpoint);
  }

  /**
   * Get asset history from /api/assets/{ticker}/history
   */
  async getAssetHistory(
    ticker: string,
    options: {
      refresh?: boolean;
      useMock?: boolean;
      daysHistory?: number;
      providers?: string[];
    } = {}
  ): Promise<AssetWithMetricsResponse> {
    const params = new URLSearchParams();

    if (options.refresh) params.append("refresh", "true");
    if (options.useMock) params.append("use_mock", "true");
    if (options.daysHistory)
      params.append("days_history", options.daysHistory.toString());
    if (options.providers?.length) {
      options.providers.forEach((provider) =>
        params.append("providers", provider)
      );
    }

    const queryString = params.toString();
    const endpoint = `/api/assets/${encodeURIComponent(ticker)}/history${
      queryString ? `?${queryString}` : ""
    }`;

    return this.makeRequest<AssetWithMetricsResponse>(endpoint);
  }

  /**
   * Get asset dividends from /api/assets/{ticker}/dividends
   */
  async getAssetDividends(
    ticker: string,
    options: {
      refresh?: boolean;
      useMock?: boolean;
      yearsHistoryDividend?: number;
      providers?: string[];
    } = {}
  ): Promise<AssetWithDividendsResponse> {
    const params = new URLSearchParams();

    if (options.refresh) params.append("refresh", "true");
    if (options.useMock) params.append("use_mock", "true");
    if (options.yearsHistoryDividend)
      params.append(
        "years_history_dividend",
        options.yearsHistoryDividend.toString()
      );
    if (options.providers?.length) {
      options.providers.forEach((provider) =>
        params.append("providers", provider)
      );
    }

    const queryString = params.toString();
    const endpoint = `/api/assets/${encodeURIComponent(ticker)}/dividends${
      queryString ? `?${queryString}` : ""
    }`;

    return this.makeRequest<AssetWithDividendsResponse>(endpoint);
  }
}

/**
 * Create a configured instance of the External API Client
 */
export function createExternalAPIClient(): ExternalAPIClient {
  const baseURL = process.env.EXTERNAL_API_BASE_URL;
  const apiKey = process.env.EXTERNAL_API_KEY;

  if (!baseURL) {
    throw new Error("EXTERNAL_API_BASE_URL environment variable is required");
  }

  if (!apiKey) {
    throw new Error("EXTERNAL_API_KEY environment variable is required");
  }

  return new ExternalAPIClient({
    baseURL,
    apiKey,
    timeout: 30000,
  });
}
