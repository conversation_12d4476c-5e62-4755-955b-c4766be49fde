import { auth } from "@/lib/auth";
import { getUserAccountData } from "@/utils/db/account-queries";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { handleGoogleOAuthWelcome } from "@/lib/google-oauth-welcome";
import MyAccountClientPage from "./ClientPage";

export default async function MyAccountPage({
  searchParams,
}: {
  searchParams: Promise<{ oauth_context?: string }>;
}) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    redirect("/auth/signin");
  }

  const resolvedSearchParams = await searchParams;

  if (resolvedSearchParams.oauth_context === "signup") {
    try {
      const result = await handleGoogleOAuthWelcome(
        session.user.email,
        session.user.name || undefined,
        session.user.id
      );

      if (result.emailSent) {
        console.log("Google OAuth welcome email sent successfully");
      }
    } catch (error) {
      console.error("Error handling Google OAuth welcome:", error);
    }

    redirect("/profile");
  }

  let accountData = null;
  try {
    accountData = await getUserAccountData(session.user.id);
  } catch (error) {
    console.error("Error fetching account data:", error);
  }

  return (
    <MyAccountClientPage initialAccountData={accountData} user={session.user} />
  );
}
