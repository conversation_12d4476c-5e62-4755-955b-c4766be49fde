/**
 * Test suite for calculatePortfolioComposition function
 * Run with: node src/tests/test-portfolio-composition.js
 */

// Mock functions
function calculatePortfolioHoldings(transactions) {
  const holdings = new Map();

  transactions.forEach((transaction) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || { quantity: 0, transactions: [] };

    const quantity =
      transaction.transaction_type === "BUY"
        ? transaction.quantity
        : -transaction.quantity;

    holdings.set(ticker, {
      quantity: existing.quantity + quantity,
      transactions: [...existing.transactions, transaction],
    });
  });

  // Filter out holdings with zero or negative quantity
  const filteredHoldings = new Map();
  holdings.forEach((holding, ticker) => {
    if (holding.quantity > 0) {
      filteredHoldings.set(ticker, holding);
    }
  });

  return filteredHoldings;
}

function convertAmount(amount, fromCurrency, toCurrency, exchangeRates) {
  if (fromCurrency === toCurrency) {
    return amount;
  }

  const rate = exchangeRates.get(`${fromCurrency}${toCurrency}.FOREX`) || 1;
  return amount * rate;
}

function calculatePortfolioComposition(
  transactions,
  latestPrices,
  assetData,
  displayCurrency = "EUR",
  exchangeRates = new Map()
) {
  const holdings = calculatePortfolioHoldings(transactions);

  // Calculate current values for each holding
  const holdingValues = new Map();
  let totalValue = 0;

  holdings.forEach((holding, ticker) => {
    const latestPrice = latestPrices.get(ticker) || 0;
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR";

    // Calculate value in original currency
    const originalValue = holding.quantity * latestPrice;

    // Convert to display currency
    const convertedValue = convertAmount(
      originalValue,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );

    holdingValues.set(ticker, convertedValue);
    totalValue += convertedValue;
  });

  // Group by different categories
  const sectorMap = new Map();
  const industryMap = new Map();
  const currencyMap = new Map();
  const countryMap = new Map();
  const assetTypeMap = new Map();
  const positionsArray = [];

  holdings.forEach((_, ticker) => {
    const value = holdingValues.get(ticker) || 0;
    const asset = assetData.get(ticker);

    if (value === 0) return;

    // Positions
    positionsArray.push({
      name: `${asset?.name} (${ticker})`,
      ticker,
      value,
      percentage: totalValue > 0 ? (value / totalValue) * 100 : 0,
    });

    // Sectors
    const sector = asset?.sector?.name || "Unknown";
    sectorMap.set(sector, (sectorMap.get(sector) || 0) + value);

    // Industries
    const industry = asset?.industry?.name || "Unknown";
    industryMap.set(industry, (industryMap.get(industry) || 0) + value);

    // Currencies
    const currency = asset?.currency?.code || "EUR";
    currencyMap.set(currency, (currencyMap.get(currency) || 0) + value);

    // Countries
    const country = asset?.country?.name || "Unknown";
    countryMap.set(country, (countryMap.get(country) || 0) + value);

    // Asset Types
    const assetType = asset?.asset_type?.name || "Stock";
    assetTypeMap.set(assetType, (assetTypeMap.get(assetType) || 0) + value);
  });

  // Convert maps to arrays with percentages
  const createCompositionArray = (map) => {
    return Array.from(map.entries())
      .map(([name, value]) => ({
        name,
        value,
        percentage: totalValue > 0 ? (value / totalValue) * 100 : 0,
      }))
      .sort((a, b) => b.value - a.value);
  };

  return {
    sectors: createCompositionArray(sectorMap),
    industries: createCompositionArray(industryMap),
    currencies: createCompositionArray(currencyMap),
    countries: createCompositionArray(countryMap),
    assetTypes: createCompositionArray(assetTypeMap),
    positions: positionsArray.sort((a, b) => b.value - a.value),
    totalValue,
    displayCurrency,
  };
}

// Test data
const mockTransactions = [
  {
    id: "1",
    ticker: "AAPL",
    transaction_type: "BUY",
    quantity: 10,
    price: 150.0,
    transaction_date: "2024-01-15",
  },
  {
    id: "2",
    ticker: "NVDA",
    transaction_type: "BUY",
    quantity: 5,
    price: 800.0,
    transaction_date: "2024-02-15",
  },
  {
    id: "3",
    ticker: "TLV.RO",
    transaction_type: "BUY",
    quantity: 100,
    price: 15.0,
    transaction_date: "2024-03-15",
  },
];

const mockLatestPrices = new Map([
  ["AAPL", 180.0],
  ["NVDA", 900.0],
  ["TLV.RO", 18.0],
]);

const mockAssetData = new Map([
  [
    "AAPL",
    {
      name: "Apple Inc.",
      currency: { code: "USD" },
      sector: { name: "Technology" },
      industry: { name: "Consumer Electronics" },
      country: { name: "United States" },
      asset_type: { name: "Stock" },
    },
  ],
  [
    "NVDA",
    {
      name: "NVIDIA Corporation",
      currency: { code: "USD" },
      sector: { name: "Technology" },
      industry: { name: "Semiconductors" },
      country: { name: "United States" },
      asset_type: { name: "Stock" },
    },
  ],
  [
    "TLV.RO",
    {
      name: "Banca Transilvania",
      currency: { code: "RON" },
      sector: { name: "Financial Services" },
      industry: { name: "Banks" },
      country: { name: "Romania" },
      asset_type: { name: "Stock" },
    },
  ],
]);

const mockExchangeRates = new Map([
  ["USDEUR.FOREX", 0.85],
  ["RONEUR.FOREX", 0.2],
]);

// Test functions
function assertEqual(actual, expected, testName, tolerance = 0.01) {
  if (Math.abs(actual - expected) < tolerance) {
    console.log(`✅ PASS: ${testName}`);
    return true;
  } else {
    console.log(`❌ FAIL: ${testName}`);
    console.log(`   Expected: ${expected}, Actual: ${actual}`);
    return false;
  }
}

function assertArrayLength(array, expectedLength, testName) {
  if (array.length === expectedLength) {
    console.log(`✅ PASS: ${testName} - Array length: ${array.length}`);
    return true;
  } else {
    console.log(`❌ FAIL: ${testName}`);
    console.log(
      `   Expected length: ${expectedLength}, Actual length: ${array.length}`
    );
    return false;
  }
}

// Run tests
console.log("🧪 Testing calculatePortfolioComposition function\n");

// Test 1: Basic composition calculation
console.log("Test 1: Basic portfolio composition");
const composition = calculatePortfolioComposition(
  mockTransactions,
  mockLatestPrices,
  mockAssetData,
  "EUR",
  mockExchangeRates
);

// Calculate expected values
// AAPL: 10 * 180 * 0.85 = 1530 EUR
// NVDA: 5 * 900 * 0.85 = 3825 EUR
// TLV.RO: 100 * 18 * 0.20 = 360 EUR (using RONEUR.FOREX rate)
// Total: 1530 + 3825 + 360 = 5715 EUR

const expectedTotal = 5715;
assertEqual(composition.totalValue, expectedTotal, "Total portfolio value");

// Test 2: Positions array
console.log("\nTest 2: Positions array");
assertArrayLength(composition.positions, 3, "Positions array length");

const nvdaPosition = composition.positions.find((p) => p.ticker === "NVDA");
if (nvdaPosition) {
  assertEqual(nvdaPosition.value, 3825, "NVDA position value");
  assertEqual(nvdaPosition.percentage, 66.93, "NVDA position percentage", 0.1); // 3825/5715 * 100
}

// Test 3: Sector composition
console.log("\nTest 3: Sector composition");
assertArrayLength(composition.sectors, 2, "Sectors array length");

const techSector = composition.sectors.find((s) => s.name === "Technology");
if (techSector) {
  assertEqual(techSector.value, 5355, "Technology sector value"); // 1530 + 3825
  assertEqual(techSector.percentage, 93.7, "Technology sector percentage", 0.1); // 5355/5715 * 100
}

// Test 4: Currency composition
console.log("\nTest 4: Currency composition");
assertArrayLength(composition.currencies, 2, "Currencies array length");

const usdCurrency = composition.currencies.find((c) => c.name === "USD");
if (usdCurrency) {
  assertEqual(usdCurrency.value, 5355, "USD currency value");
  assertEqual(usdCurrency.percentage, 93.7, "USD currency percentage", 0.1); // 5355/5715 * 100
}

// Test 5: Empty portfolio
console.log("\nTest 5: Empty portfolio");
const emptyComposition = calculatePortfolioComposition(
  [],
  mockLatestPrices,
  mockAssetData,
  "EUR",
  mockExchangeRates
);

assertEqual(emptyComposition.totalValue, 0, "Empty portfolio total value");
assertArrayLength(emptyComposition.positions, 0, "Empty portfolio positions");

// Test 6: Zero prices
console.log("\nTest 6: Zero prices");
const zeroPrices = new Map([
  ["AAPL", 0],
  ["NVDA", 0],
  ["TLV.RO", 0],
]);

const zeroComposition = calculatePortfolioComposition(
  mockTransactions,
  zeroPrices,
  mockAssetData,
  "EUR",
  mockExchangeRates
);

assertEqual(zeroComposition.totalValue, 0, "Zero prices total value");

// Test 7: Same currency (no conversion)
console.log("\nTest 7: Same currency calculation");
const usdComposition = calculatePortfolioComposition(
  mockTransactions.slice(0, 2), // Only USD assets
  mockLatestPrices,
  mockAssetData,
  "USD",
  mockExchangeRates
);

// AAPL: 10 * 180 = 1800 USD
// NVDA: 5 * 900 = 4500 USD
// Total: 6300 USD
assertEqual(usdComposition.totalValue, 6300, "USD portfolio total value");

console.log("\n🏁 Test suite completed!");
