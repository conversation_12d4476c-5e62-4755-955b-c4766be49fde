"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";

interface PortfolioErrorStateProps {
  error?: Error | string;
  onRetry?: () => void;
  className?: string;
}

export function PortfolioErrorState({ 
  error, 
  onRetry, 
  className 
}: PortfolioErrorStateProps) {
  const errorMessage = typeof error === "string" ? error : error?.message;

  return (
    <div className={cn("flex items-center justify-center py-12", className)}>
      <Card className="max-w-md w-full">
        <CardContent className="pt-6">
          <div className="text-center space-y-6">
            {/* Error Icon */}
            <div className="mx-auto w-16 h-16 bg-red-50 dark:bg-red-900/20 rounded-full flex items-center justify-center">
              <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>

            {/* Error Content */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
                Eroare la încărcarea portofoliilor
              </h3>
              <p className="text-red-700 dark:text-red-300 text-sm leading-relaxed">
                {errorMessage || 
                  "A apărut o eroare neașteptată la încărcarea portofoliilor. Te rugăm să încerci din nou."}
              </p>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              {onRetry && (
                <Button 
                  onClick={onRetry} 
                  variant="outline" 
                  className="w-full gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Încearcă din nou
                </Button>
              )}
              
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => window.location.reload()}
                className="w-full text-xs"
              >
                Reîncarcă pagina
              </Button>
            </div>

            {/* Help Text */}
            <div className="pt-4 border-t">
              <p className="text-xs text-muted-foreground">
                Dacă problema persistă, te rugăm să contactezi suportul tehnic.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
