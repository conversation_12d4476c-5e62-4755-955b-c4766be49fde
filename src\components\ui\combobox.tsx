"use client";

import * as React from "react";
import { Check, ChevronDown, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

export interface ComboboxOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

interface ComboboxProps {
  options: ComboboxOption[];
  value?: string;
  onValueChange?: (value: string) => void;
  onSearchChange?: (search: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  disabled?: boolean;
  loading?: boolean;
  loadingMessage?: string;
  error?: boolean;
  className?: string;
  searchValue?: string;
  allowCustomValue?: boolean;
  renderOption?: (option: ComboboxOption) => React.ReactNode;
  renderSelected?: (option: ComboboxOption | null) => React.ReactNode;
}

export function Combobox({
  options,
  value,
  onValueChange,
  onSearchChange,
  placeholder = "Selectează o opțiune...",
  searchPlaceholder = "Caută...",
  emptyMessage = "Nu s-au găsit rezultate.",
  disabled = false,
  loading = false,
  loadingMessage = "Se caută...",
  error = false,
  className,
  searchValue = "",
  allowCustomValue = false,
  renderOption,
  renderSelected,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [internalSearchValue, setInternalSearchValue] =
    React.useState(searchValue);
  const [highlightedIndex, setHighlightedIndex] = React.useState(-1);

  const selectedOption = options.find((option) => option.value === value);

  // Update internal search value when external search value changes
  React.useEffect(() => {
    setInternalSearchValue(searchValue);
  }, [searchValue]);

  // Handle search input change
  const handleSearchChange = (newValue: string) => {
    setInternalSearchValue(newValue);
    onSearchChange?.(newValue);
    setHighlightedIndex(-1);
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!open) return;

    const availableOptions = options.filter((option) => !option.disabled);

    switch (event.key) {
      case "ArrowDown":
        event.preventDefault();
        setHighlightedIndex((prev) =>
          prev < availableOptions.length - 1 ? prev + 1 : 0
        );
        break;
      case "ArrowUp":
        event.preventDefault();
        setHighlightedIndex((prev) =>
          prev > 0 ? prev - 1 : availableOptions.length - 1
        );
        break;
      case "Enter":
        event.preventDefault();
        if (
          highlightedIndex >= 0 &&
          highlightedIndex < availableOptions.length
        ) {
          const selectedOption = availableOptions[highlightedIndex];
          onValueChange?.(selectedOption.value);
          setOpen(false);
        } else if (allowCustomValue && internalSearchValue.trim()) {
          onValueChange?.(internalSearchValue.trim());
          setOpen(false);
        }
        break;
      case "Escape":
        setOpen(false);
        break;
    }
  };

  // Handle option selection
  const handleOptionSelect = (optionValue: string) => {
    onValueChange?.(optionValue);
    setOpen(false);
  };

  // Reset highlighted index when options change
  React.useEffect(() => {
    setHighlightedIndex(-1);
  }, [options]);

  return (
    <Popover modal={true} open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            error && "border-red-500 dark:border-red-400",
            className
          )}
          disabled={disabled}
        >
          <span className="truncate">
            {selectedOption
              ? renderSelected
                ? renderSelected(selectedOption)
                : selectedOption.label
              : placeholder}
          </span>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
        <div className="flex items-center border-b px-3">
          <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
          <Input
            placeholder={searchPlaceholder}
            value={internalSearchValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            onKeyDown={handleKeyDown}
            className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            autoFocus
          />
        </div>
        <div className="max-h-60 overflow-auto p-1">
          {loading ? (
            <div className="flex items-center justify-center py-6">
              <LoadingSpinner size="sm" />
              <span className="ml-2 text-sm text-muted-foreground">
                {loadingMessage}
              </span>
            </div>
          ) : options.length === 0 ? (
            <div className="py-6 text-center text-sm text-muted-foreground">
              {emptyMessage}
            </div>
          ) : (
            options.map((option) => {
              const availableOptions = options.filter((opt) => !opt.disabled);
              const availableIndex = availableOptions.findIndex(
                (opt) => opt.value === option.value
              );
              const isHighlighted = availableIndex === highlightedIndex;

              return (
                <div
                  key={option.value}
                  className={cn(
                    "group relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none mb-1",
                    option.disabled
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer hover:bg-accent hover:text-accent-foreground",
                    isHighlighted && "bg-accent text-accent-foreground",
                    value === option.value && "bg-accent text-accent-foreground"
                  )}
                  onClick={() =>
                    !option.disabled && handleOptionSelect(option.value)
                  }
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === option.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex-1">
                    {renderOption ? (
                      renderOption(option)
                    ) : (
                      <div>
                        <div>{option.label}</div>
                        {option.description && (
                          <div className="text-xs text-muted-foreground">
                            {option.description}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
