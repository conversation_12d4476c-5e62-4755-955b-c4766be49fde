import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import {
  getTransactionById,
  updateTransaction,
  deleteTransaction,
  getPortfolioById,
} from "@/utils/db/portfolio-queries";
import {
  updateTransactionInputSchema,
  UpdateTransactionInputData,
} from "@/lib/transaction-schemas";
import { z } from "zod";

// GET /api/transactions/[id] - Get a specific transaction
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const { id } = await params;
    const transactionId = id;

    if (!transactionId) {
      return NextResponse.json(
        { error: "ID-ul tranzacției este obligatoriu" },
        { status: 400 }
      );
    }

    const transaction = await getTransactionById(transactionId);

    if (!transaction) {
      return NextResponse.json(
        { error: "Tranzacția nu a fost găsită" },
        { status: 404 }
      );
    }

    // Verify that the transaction belongs to a portfolio owned by the user
    const portfolio = await getPortfolioById(transaction.portfolio_id);
    if (!portfolio || portfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să accesezi această tranzacție" },
        { status: 403 }
      );
    }

    return NextResponse.json({
      transaction,
      message: "Tranzacția a fost încărcată cu succes",
    });
  } catch (error) {
    console.error("Error fetching transaction:", error);
    return NextResponse.json(
      { error: "Nu s-a putut încărca tranzacția" },
      { status: 500 }
    );
  }
}

// PUT /api/transactions/[id] - Update a specific transaction
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const { id } = await params;
    const transactionId = id;

    if (!transactionId) {
      return NextResponse.json(
        { error: "ID-ul tranzacției este obligatoriu" },
        { status: 400 }
      );
    }

    // Check if transaction exists and user has permission
    const existingTransaction = await getTransactionById(transactionId);
    if (!existingTransaction) {
      return NextResponse.json(
        { error: "Tranzacția nu a fost găsită" },
        { status: 404 }
      );
    }

    const portfolio = await getPortfolioById(existingTransaction.portfolio_id);
    if (!portfolio || portfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să modifici această tranzacție" },
        { status: 403 }
      );
    }

    const body = await request.json();
    console.log("PUT /api/transactions/[id] - Request body:", body);

    // Validate request body using the update schema
    let validatedData: UpdateTransactionInputData;
    try {
      validatedData = updateTransactionInputSchema.parse(body);
      console.log(
        "PUT /api/transactions/[id] - Validated data:",
        validatedData
      );
    } catch (error) {
      console.error("PUT /api/transactions/[id] - Validation error:", error);
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Date invalide", details: error.errors },
          { status: 400 }
        );
      }
      throw error;
    }

    // Map API field names to database field names
    const dbUpdates: {
      ticker?: string;
      price?: number;
      quantity?: number;
      transaction_date?: string;
      transaction_type?: "BUY" | "SELL";
      transaction_fee?: number;
      notes?: string;
    } = {};

    if (validatedData.ticker !== undefined) {
      dbUpdates.ticker = validatedData.ticker;
    }
    if (validatedData.price !== undefined) {
      dbUpdates.price = validatedData.price;
    }
    if (validatedData.quantity !== undefined) {
      dbUpdates.quantity = validatedData.quantity;
    }
    if (validatedData.transactionDate !== undefined) {
      dbUpdates.transaction_date = validatedData.transactionDate;
    }
    if (validatedData.type !== undefined) {
      dbUpdates.transaction_type = validatedData.type;
    }
    if (validatedData.notes !== undefined) {
      dbUpdates.notes = validatedData.notes;
    }
    if (validatedData.transaction_fee !== undefined) {
      dbUpdates.transaction_fee = validatedData.transaction_fee;
    }

    console.log("PUT /api/transactions/[id] - DB updates:", dbUpdates);

    const updatedTransaction = await updateTransaction(
      transactionId,
      dbUpdates
    );

    console.log(
      "PUT /api/transactions/[id] - Updated transaction:",
      updatedTransaction
    );

    return NextResponse.json({
      transaction: updatedTransaction,
      message: "Tranzacția a fost actualizată cu succes",
    });
  } catch (error) {
    console.error("Error updating transaction:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Date invalide", details: error.errors },
        { status: 400 }
      );
    }

    if (
      error instanceof Error &&
      error.message === "Tranzacția nu a fost găsită"
    ) {
      return NextResponse.json(
        { error: "Tranzacția nu a fost găsită" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Nu s-a putut actualiza tranzacția" },
      { status: 500 }
    );
  }
}

// DELETE /api/transactions/[id] - Delete a specific transaction
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const { id } = await params;
    const transactionId = id;

    if (!transactionId) {
      return NextResponse.json(
        { error: "ID-ul tranzacției este obligatoriu" },
        { status: 400 }
      );
    }

    const existingTransaction = await getTransactionById(transactionId);
    if (!existingTransaction) {
      return NextResponse.json(
        { error: "Tranzacția nu a fost găsită" },
        { status: 404 }
      );
    }

    const portfolio = await getPortfolioById(existingTransaction.portfolio_id);
    if (!portfolio || portfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să ștergi această tranzacție" },
        { status: 403 }
      );
    }

    await deleteTransaction(transactionId);

    return NextResponse.json({
      message: "Tranzacția a fost ștearsă cu succes",
    });
  } catch (error) {
    console.error("Error deleting transaction:", error);

    if (
      error instanceof Error &&
      error.message === "Tranzacția nu a fost găsită"
    ) {
      return NextResponse.json(
        { error: "Tranzacția nu a fost găsită" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Nu s-a putut șterge tranzacția" },
      { status: 500 }
    );
  }
}
