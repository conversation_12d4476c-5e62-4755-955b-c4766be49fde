/**
 * Test runner for all dashboard calculation tests
 * Run with: node src/tests/run-all-tests.js
 */

const { spawn } = require('child_process');
const path = require('path');

// List of test files to run
const testFiles = [
  'test-portfolio-holdings-at-date.js',
  'test-ticker-dividend-income.js',
  'test-portfolio-composition.js',
  'test-portfolio-metrics.js',
  'test-company-holdings.js',
  'test-currency-conversion.js',
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function runTest(testFile) {
  return new Promise((resolve) => {
    console.log(colorize(`\n${'='.repeat(60)}`, 'cyan'));
    console.log(colorize(`Running: ${testFile}`, 'bright'));
    console.log(colorize(`${'='.repeat(60)}`, 'cyan'));

    const testPath = path.join(__dirname, testFile);
    const child = spawn('node', [testPath], {
      stdio: 'pipe',
      cwd: process.cwd(),
    });

    let output = '';
    let errorOutput = '';

    child.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      process.stdout.write(text);
    });

    child.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      process.stderr.write(colorize(text, 'red'));
    });

    child.on('close', (code) => {
      const passed = output.includes('✅') && !output.includes('❌');
      const testResult = {
        file: testFile,
        passed,
        code,
        output,
        errorOutput,
      };

      if (code === 0 && passed) {
        console.log(colorize(`\n✅ ${testFile} PASSED`, 'green'));
      } else {
        console.log(colorize(`\n❌ ${testFile} FAILED (exit code: ${code})`, 'red'));
      }

      resolve(testResult);
    });

    child.on('error', (error) => {
      console.error(colorize(`Error running ${testFile}: ${error.message}`, 'red'));
      resolve({
        file: testFile,
        passed: false,
        code: 1,
        output: '',
        errorOutput: error.message,
      });
    });
  });
}

async function runAllTests() {
  console.log(colorize('🧪 Dashboard Calculation Functions Test Suite', 'bright'));
  console.log(colorize('=' .repeat(60), 'cyan'));
  console.log(colorize(`Running ${testFiles.length} test files...`, 'yellow'));

  const startTime = Date.now();
  const results = [];

  for (const testFile of testFiles) {
    const result = await runTest(testFile);
    results.push(result);
  }

  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  // Summary
  console.log(colorize('\n' + '='.repeat(60), 'cyan'));
  console.log(colorize('📊 TEST SUMMARY', 'bright'));
  console.log(colorize('='.repeat(60), 'cyan'));

  const passed = results.filter(r => r.passed).length;
  const failed = results.filter(r => !r.passed).length;

  console.log(`Total tests: ${results.length}`);
  console.log(colorize(`Passed: ${passed}`, 'green'));
  console.log(colorize(`Failed: ${failed}`, failed > 0 ? 'red' : 'green'));
  console.log(`Duration: ${duration}s`);

  // Detailed results
  console.log(colorize('\n📋 DETAILED RESULTS:', 'bright'));
  results.forEach((result, index) => {
    const status = result.passed ? 
      colorize('✅ PASS', 'green') : 
      colorize('❌ FAIL', 'red');
    
    console.log(`${index + 1}. ${result.file} - ${status}`);
    
    if (!result.passed && result.errorOutput) {
      console.log(colorize(`   Error: ${result.errorOutput}`, 'red'));
    }
  });

  // Overall result
  console.log(colorize('\n' + '='.repeat(60), 'cyan'));
  if (failed === 0) {
    console.log(colorize('🎉 ALL TESTS PASSED!', 'green'));
    process.exit(0);
  } else {
    console.log(colorize(`💥 ${failed} TEST(S) FAILED!`, 'red'));
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log(colorize('\n\n⚠️  Test execution interrupted by user', 'yellow'));
  process.exit(130);
});

process.on('uncaughtException', (error) => {
  console.error(colorize(`\n💥 Uncaught exception: ${error.message}`, 'red'));
  console.error(error.stack);
  process.exit(1);
});

// Run the tests
runAllTests().catch((error) => {
  console.error(colorize(`💥 Test runner error: ${error.message}`, 'red'));
  console.error(error.stack);
  process.exit(1);
});
