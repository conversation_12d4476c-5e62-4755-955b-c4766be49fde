"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format, parse, isValid } from "date-fns";
import { ro } from "date-fns/locale/ro";
import { Calendar as CalendarIcon, Edit3 } from "lucide-react";
import { useState, useRef, useEffect } from "react";

interface DatePickerProps {
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  maxDate?: Date;
  minDate?: Date;
  error?: boolean;
  id?: string;
  name?: string;
  allowManualInput?: boolean;
}

// Date parsing utility functions
const parseDate = (input: string): Date | null => {
  if (!input.trim()) return null;

  // Clean the input - remove extra spaces and normalize separators
  const cleanInput = input.trim().replace(/\s+/g, " ");

  // Try different date formats in order of preference
  const formats = [
    "dd/MM/yyyy",
    "dd-MM-yyyy",
    "dd.MM.yyyy",
    "yyyy-MM-dd",
    "dd/MM/yy",
    "dd-MM-yy",
    "dd.MM.yy",
    "d/M/yyyy",
    "d-M-yyyy",
    "d.M.yyyy",
    "d/M/yy",
    "d-M-yy",
    "d.M.yy",
  ];

  for (const formatStr of formats) {
    try {
      const parsed = parse(cleanInput, formatStr, new Date());
      if (isValid(parsed)) {
        // Additional validation for reasonable dates
        const year = parsed.getFullYear();
        if (year >= 1900 && year <= 2100) {
          return parsed;
        }
      }
    } catch {
      // Continue to next format
    }
  }

  // Try parsing with native Date constructor as fallback
  try {
    const nativeDate = new Date(cleanInput);
    if (isValid(nativeDate)) {
      const year = nativeDate.getFullYear();
      if (year >= 1900 && year <= 2100) {
        return nativeDate;
      }
    }
  } catch {
    // Ignore
  }

  return null;
};

const formatDateForDisplay = (date: Date): string => {
  return format(date, "dd/MM/yyyy");
};

// Get a user-friendly error message for invalid dates
const getDateErrorMessage = (
  input: string,
  maxDate?: Date,
  minDate?: Date
): string => {
  if (!input.trim()) return "Data este obligatorie";

  const parsed = parseDate(input);
  if (!parsed) return "Format de dată invalid. Folosește dd/mm/yyyy";

  if (maxDate && parsed > maxDate) {
    return `Data nu poate fi după ${formatDateForDisplay(maxDate)}`;
  }

  if (minDate && parsed < minDate) {
    return `Data nu poate fi înainte de ${formatDateForDisplay(minDate)}`;
  }

  return "";
};

export function DatePicker({
  value,
  onChange,
  placeholder = "Selectează data",
  disabled = false,
  className,
  maxDate,
  minDate,
  error = false,
  id,
  allowManualInput = true,
}: DatePickerProps) {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [isManualInput, setIsManualInput] = useState(false);
  const [inputError, setInputError] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  // Update input value when value prop changes
  useEffect(() => {
    if (value && !isManualInput) {
      setInputValue(formatDateForDisplay(value));
    } else if (!value && !isManualInput) {
      setInputValue("");
    }
  }, [value, isManualInput]);

  const handleSelect = (date: Date | undefined) => {
    onChange?.(date);
    setIsManualInput(false);

    if (date) {
      setOpen(false);
      setInputValue(formatDateForDisplay(date));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setIsManualInput(true);

    // Clear the value and error if input is empty
    if (newValue === "") {
      onChange?.(undefined);
      setInputError("");
      return;
    }

    // Try to parse the date as user types
    const parsedDate = parseDate(newValue);
    if (parsedDate) {
      // Validate against min/max dates
      const isValidDate =
        (!minDate || parsedDate >= minDate) &&
        (!maxDate || parsedDate <= maxDate);

      if (isValidDate) {
        onChange?.(parsedDate);
        setInputError("");
      } else {
        setInputError(getDateErrorMessage(newValue, maxDate, minDate));
      }
    } else {
      // Set error for invalid format, but don't call onChange yet
      setInputError(getDateErrorMessage(newValue, maxDate, minDate));
    }
  };

  const handleInputBlur = () => {
    setIsManualInput(false);
    setInputError("");
    // If we have a valid value, format it properly
    if (value) {
      setInputValue(formatDateForDisplay(value));
    } else {
      setInputValue("");
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      inputRef.current?.blur();
    } else if (e.key === "Escape") {
      e.preventDefault();
      setIsManualInput(false);
      setInputError("");
      if (value) {
        setInputValue(formatDateForDisplay(value));
      } else {
        setInputValue("");
      }
      inputRef.current?.blur();
    }
  };

  const toggleManualInput = () => {
    if (allowManualInput) {
      setIsManualInput(true);
      setTimeout(() => {
        inputRef.current?.focus();
        inputRef.current?.select();
      }, 0);
    }
  };

  if (allowManualInput && isManualInput) {
    const hasInputError = inputError !== "";
    return (
      <div className="relative">
        <Input
          ref={inputRef}
          id={id}
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onKeyDown={handleInputKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            "w-full pl-10",
            (error || hasInputError) && "border-red-500 dark:border-red-400",
            className
          )}
        />
        <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        {hasInputError && (
          <div className="absolute top-full left-0 mt-1 text-xs text-red-600 dark:text-red-400">
            {inputError}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant="outline"
            data-empty={!value}
            disabled={disabled}
            className={cn(
              "w-full justify-start text-left font-normal data-[empty=true]:text-muted-foreground pr-10",
              error && "border-red-500 dark:border-red-400",
              className
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {value ? format(value, "dd/MM/yyyy") : <span>{placeholder}</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={value}
            onSelect={handleSelect}
            locale={ro}
            captionLayout="dropdown"
            disabled={(date) => {
              if (disabled) return true;
              if (maxDate && date > maxDate) return true;
              if (minDate && date < minDate) return true;
              return false;
            }}
            autoFocus
          />
        </PopoverContent>
      </Popover>

      {allowManualInput && !disabled && (
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleManualInput}
          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-muted"
          title="Editează manual data"
        >
          <Edit3 className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
}
