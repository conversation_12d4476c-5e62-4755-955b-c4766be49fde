-- Add price column to ptvuser_transactions table
-- This column will store the individual transaction price for each BUY/SELL transaction

ALTER TABLE ptvuser.transactions 
ADD COLUMN IF NOT EXISTS price DECIMAL(15,8);

-- Add comment to the new column
COMMENT ON COLUMN ptvuser.transactions.price IS 'Individual transaction price per unit (supports up to 8 decimal places)';

-- Update existing transactions to have a default price of 0 (will need to be updated manually or through data migration)
UPDATE ptvuser.transactions 
SET price = 0.00000000 
WHERE price IS NULL;

-- Make the price column NOT NULL after setting default values
ALTER TABLE ptvuser.transactions 
ALTER COLUMN price SET NOT NULL;

-- Add a check constraint to ensure price is positive
ALTER TABLE ptvuser.transactions 
ADD CONSTRAINT transactions_price_positive CHECK (price >= 0);
