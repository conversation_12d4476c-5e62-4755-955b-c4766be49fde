/**
 * Test suite for calculatePortfolioMetrics function
 * Run with: node src/tests/test-portfolio-metrics.js
 */

// Mock functions
function calculatePortfolioHoldings(transactions) {
  const holdings = new Map();

  transactions.forEach((transaction) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || { quantity: 0, transactions: [] };

    const quantity =
      transaction.transaction_type === "BUY"
        ? transaction.quantity
        : -transaction.quantity;

    holdings.set(ticker, {
      quantity: existing.quantity + quantity,
      transactions: [...existing.transactions, transaction],
    });
  });

  return holdings;
}

function convertAmount(amount, fromCurrency, toCurrency, exchangeRates) {
  if (fromCurrency === toCurrency) {
    return amount;
  }

  const rate = exchangeRates.get(`${fromCurrency}${toCurrency}.FOREX`) || 1;
  return amount * rate;
}

function calculatePortfolioMetrics(
  transactions,
  latestPrices,
  assetData,
  dividends,
  displayCurrency = "EUR",
  exchangeRates = new Map()
) {
  const currentHoldings = calculatePortfolioHoldings(transactions);

  // 1. Capital Investit (Total Invested Capital)
  let capitalInvested = 0;
  currentHoldings.forEach((holding, ticker) => {
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR";

    // Calculate total cost basis for current holdings
    let totalCostBasis = 0;
    let totalBuyQuantity = 0;

    holding.transactions.forEach((t) => {
      if (t.transaction_type === "BUY") {
        totalCostBasis += t.price * t.quantity;
        totalBuyQuantity += t.quantity;
      }
    });

    const avgCostPerShare =
      totalBuyQuantity > 0 ? totalCostBasis / totalBuyQuantity : 0;
    const costBasisInOriginalCurrency = holding.quantity * avgCostPerShare;

    const convertedCostBasis = convertAmount(
      costBasisInOriginalCurrency,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );

    capitalInvested += convertedCostBasis;
  });

  // 2. Current Market Value
  let currentMarketValue = 0;
  currentHoldings.forEach((holding, ticker) => {
    const latestPrice = latestPrices.get(ticker) || 0;
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR";

    const originalValue = holding.quantity * latestPrice;
    const convertedValue = convertAmount(
      originalValue,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );

    currentMarketValue += convertedValue;
  });

  // 3. Price Gain (unrealized)
  const priceGain = currentMarketValue - capitalInvested;
  const priceGainPercentage =
    capitalInvested > 0 ? (priceGain / capitalInvested) * 100 : 0;

  // 4. Dividends Income (simplified - just sum all dividends)
  let dividendsIncome = 0;
  dividends.forEach((dividend) => {
    const dividendCurrency = dividend.asset?.currency?.code || "EUR";
    const convertedDividend = convertAmount(
      dividend.amount_per_share * 10, // Simplified: assume 10 shares for each dividend
      dividendCurrency,
      displayCurrency,
      exchangeRates
    );
    dividendsIncome += convertedDividend;
  });

  const dividendsPercentage =
    capitalInvested > 0 ? (dividendsIncome / capitalInvested) * 100 : 0;

  // 5. Realized Gain (from SELL transactions)
  let realizedGain = 0;
  transactions.forEach((transaction) => {
    if (transaction.transaction_type === "SELL") {
      const asset = assetData.get(transaction.ticker);
      const assetCurrency = asset?.currency?.code || "EUR";

      // Simplified: assume average cost of 100 for calculation
      const gain = (transaction.price - 100) * transaction.quantity;
      const convertedGain = convertAmount(
        gain,
        assetCurrency,
        displayCurrency,
        exchangeRates
      );
      realizedGain += convertedGain;
    }
  });

  const realizedGainPercentage =
    capitalInvested > 0 ? (realizedGain / capitalInvested) * 100 : 0;

  // 6. Transaction Costs
  let transactionCosts = 0;
  transactions.forEach((transaction) => {
    if (transaction.transaction_fee && transaction.transaction_fee > 0) {
      const asset = assetData.get(transaction.ticker);
      const assetCurrency = asset?.currency?.code || "EUR";

      const convertedFee = convertAmount(
        transaction.transaction_fee,
        assetCurrency,
        displayCurrency,
        exchangeRates
      );

      transactionCosts += convertedFee;
    }
  });

  // 7. Total Return
  const totalReturn = priceGain + dividendsIncome + realizedGain;

  return {
    capitalInvested,
    priceGain,
    priceGainPercentage,
    dividends: dividendsIncome,
    dividendsPercentage,
    realizedGain,
    realizedGainPercentage,
    transactionCosts,
    taxes: transactionCosts, // Simplified
    totalReturn,
    twrr: 0, // Simplified
    mwrr: 0, // Simplified
    displayCurrency,
  };
}

// Test data
const mockTransactions = [
  {
    id: "1",
    ticker: "AAPL",
    transaction_type: "BUY",
    quantity: 10,
    price: 150.0,
    transaction_date: "2024-01-15",
    transaction_fee: 5.0,
  },
  {
    id: "2",
    ticker: "AAPL",
    transaction_type: "BUY",
    quantity: 5,
    price: 160.0,
    transaction_date: "2024-02-15",
    transaction_fee: 3.0,
  },
  {
    id: "3",
    ticker: "AAPL",
    transaction_type: "SELL",
    quantity: 3,
    price: 170.0,
    transaction_date: "2024-03-15",
    transaction_fee: 2.0,
  },
  {
    id: "4",
    ticker: "NVDA",
    transaction_type: "BUY",
    quantity: 5,
    price: 800.0,
    transaction_date: "2024-02-01",
    transaction_fee: 10.0,
  },
];

const mockLatestPrices = new Map([
  ["AAPL", 180.0],
  ["NVDA", 900.0],
]);

const mockAssetData = new Map([
  [
    "AAPL",
    {
      name: "Apple Inc.",
      currency: { code: "USD" },
    },
  ],
  [
    "NVDA",
    {
      name: "NVIDIA Corporation",
      currency: { code: "USD" },
    },
  ],
]);

const mockDividends = [
  {
    dividend_id: 1,
    asset_id: 213,
    ex_date: "2024-02-01",
    amount_per_share: 0.25,
    dividend_type: "Regular",
    status: "Paid",
    asset: { ticker: "AAPL", currency: { code: "USD" } },
  },
  {
    dividend_id: 2,
    asset_id: 214,
    ex_date: "2024-03-01",
    amount_per_share: 0.01,
    dividend_type: "Regular",
    status: "Paid",
    asset: { ticker: "NVDA", currency: { code: "USD" } },
  },
];

const mockExchangeRates = new Map([["USDEUR.FOREX", 0.85]]);

// Test functions
function assertEqual(actual, expected, testName, tolerance = 0.01) {
  if (Math.abs(actual - expected) < tolerance) {
    console.log(`✅ PASS: ${testName}`);
    console.log(`   Expected: ${expected}, Actual: ${actual}`);
    return true;
  } else {
    console.log(`❌ FAIL: ${testName}`);
    console.log(`   Expected: ${expected}, Actual: ${actual}`);
    return false;
  }
}

// Run tests
console.log("🧪 Testing calculatePortfolioMetrics function\n");

// Test 1: Basic metrics calculation
console.log("Test 1: Basic portfolio metrics");
const metrics = calculatePortfolioMetrics(
  mockTransactions,
  mockLatestPrices,
  mockAssetData,
  mockDividends,
  "EUR",
  mockExchangeRates
);

// Calculate expected values
// Current holdings: AAPL: 12 shares, NVDA: 5 shares
// AAPL cost basis: (10*150 + 5*160) / 15 = 153.33 per share, 12 shares = 1840
// NVDA cost basis: 5*800 = 4000
// Total capital invested: (1840 + 4000) * 0.85 = 4964 EUR

// Current market value: (12*180 + 5*900) * 0.85 = 6120 * 0.85 = 5202 EUR
// Price gain: 5202 - 4964 = 238 EUR

console.log("Portfolio Metrics:");
console.log(
  `Capital Invested: ${metrics.capitalInvested.toFixed(2)} ${
    metrics.displayCurrency
  }`
);
console.log(
  `Current Market Value: ${(
    metrics.capitalInvested + metrics.priceGain
  ).toFixed(2)} ${metrics.displayCurrency}`
);
console.log(
  `Price Gain: ${metrics.priceGain.toFixed(2)} ${metrics.displayCurrency}`
);
console.log(`Price Gain %: ${metrics.priceGainPercentage.toFixed(2)}%`);
console.log(
  `Dividends: ${metrics.dividends.toFixed(2)} ${metrics.displayCurrency}`
);
console.log(
  `Transaction Costs: ${metrics.transactionCosts.toFixed(2)} ${
    metrics.displayCurrency
  }`
);
console.log(
  `Total Return: ${metrics.totalReturn.toFixed(2)} ${metrics.displayCurrency}`
);

// Test 2: Capital invested calculation
console.log("\nTest 2: Capital invested calculation");
const expectedCapitalInvested = 4964; // (1840 + 4000) * 0.85
assertEqual(
  metrics.capitalInvested,
  expectedCapitalInvested,
  "Capital invested",
  1
);

// Test 3: Transaction costs calculation
console.log("\nTest 3: Transaction costs calculation");
const expectedTransactionCosts = (5 + 3 + 2 + 10) * 0.85; // 20 * 0.85 = 17
assertEqual(
  metrics.transactionCosts,
  expectedTransactionCosts,
  "Transaction costs"
);

// Test 4: Currency consistency
console.log("\nTest 4: Currency consistency");
if (metrics.displayCurrency === "EUR") {
  console.log("✅ PASS: Display currency is EUR");
} else {
  console.log(`❌ FAIL: Expected EUR, got ${metrics.displayCurrency}`);
}

// Test 5: Empty portfolio
console.log("\nTest 5: Empty portfolio");
const emptyMetrics = calculatePortfolioMetrics(
  [],
  mockLatestPrices,
  mockAssetData,
  [],
  "EUR",
  mockExchangeRates
);

assertEqual(
  emptyMetrics.capitalInvested,
  0,
  "Empty portfolio capital invested"
);
assertEqual(emptyMetrics.priceGain, 0, "Empty portfolio price gain");
assertEqual(emptyMetrics.dividends, 0, "Empty portfolio dividends");

// Test 6: Same currency (no conversion)
console.log("\nTest 6: Same currency calculation");
const usdMetrics = calculatePortfolioMetrics(
  mockTransactions,
  mockLatestPrices,
  mockAssetData,
  mockDividends,
  "USD",
  mockExchangeRates
);

// Capital invested: 1840 + 4000 = 5840 USD
assertEqual(usdMetrics.capitalInvested, 5840, "USD capital invested");

// Test 7: Percentage calculations
console.log("\nTest 7: Percentage calculations");
const priceGainPercentage = (metrics.priceGain / metrics.capitalInvested) * 100;
assertEqual(
  metrics.priceGainPercentage,
  priceGainPercentage,
  "Price gain percentage",
  0.1
);

if (metrics.dividendsPercentage >= 0) {
  console.log("✅ PASS: Dividends percentage is non-negative");
} else {
  console.log("❌ FAIL: Dividends percentage is negative");
}

console.log("\n🏁 Test suite completed!");
