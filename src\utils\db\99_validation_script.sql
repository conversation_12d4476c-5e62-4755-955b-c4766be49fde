-- DATABASE SCHEMA VALIDATION SCRIPT
-- This script validates that all required tables and columns exist
-- Run this after the recovery scripts to ensure completeness

-- Check that all required tables exist
DO $$
DECLARE
    missing_tables TEXT[] := ARRAY[]::TEXT[];
    table_name TEXT;
    required_tables TEXT[] := ARRAY[
        'user',
        'session', 
        'account',
        'verification',
        'ptvuser.currency',
        'ptvuser.country',
        'ptvuser.sector',
        'ptvuser.industry',
        'ptvuser.asset_type',
        'ptvuser.exchange',
        'ptvuser.portfolios',
        'ptvuser.transactions',
        'ptvuser.asset',
        'ptvuser.asset_prices',
        'ptvuser.asset_metrics',
        'ptvuser.dividend',
        'ptvuser.profile_pictures',
        'ptvuser.search_results',
        'ptvuser_contact_submissions',
        'support_faq',
        'ptvuser.mail_config'
    ];
BEGIN
    FOREACH table_name IN ARRAY required_tables
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema || '.' || table_name = table_name
            OR (table_schema = 'public' AND table_name = table_name)
        ) THEN
            missing_tables := array_append(missing_tables, table_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION 'Missing tables: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE 'All required tables exist ✓';
    END IF;
END $$;

-- Check critical columns exist
DO $$
DECLARE
    missing_columns TEXT[] := ARRAY[]::TEXT[];
    column_check RECORD;
    critical_columns CURSOR FOR
        SELECT 'user' as table_name, 'id' as column_name
        UNION ALL SELECT 'user', 'email'
        UNION ALL SELECT 'user', 'emailVerified'
        UNION ALL SELECT 'ptvuser.portfolios', 'id'
        UNION ALL SELECT 'ptvuser.portfolios', 'user_id'
        UNION ALL SELECT 'ptvuser.transactions', 'id'
        UNION ALL SELECT 'ptvuser.transactions', 'portfolio_id'
        UNION ALL SELECT 'ptvuser.transactions', 'ticker'
        UNION ALL SELECT 'ptvuser.transactions', 'transaction_fee'
        UNION ALL SELECT 'ptvuser.asset', 'asset_id'
        UNION ALL SELECT 'ptvuser.asset', 'ticker'
        UNION ALL SELECT 'ptvuser.asset_prices', 'asset_id'
        UNION ALL SELECT 'ptvuser.asset_prices', 'adj_close'
        UNION ALL SELECT 'ptvuser.asset_metrics', 'fifty_two_week_low'
        UNION ALL SELECT 'ptvuser.asset_metrics', 'fifty_two_week_high'
        UNION ALL SELECT 'ptvuser.asset_metrics', 'dividend_yield'
        UNION ALL SELECT 'ptvuser.dividend', 'dividend_id'
        UNION ALL SELECT 'ptvuser.dividend', 'asset_id'
        UNION ALL SELECT 'ptvuser.dividend', 'ex_date'
        UNION ALL SELECT 'ptvuser.dividend', 'amount_per_share'
        UNION ALL SELECT 'ptvuser.dividend', 'status';
BEGIN
    FOR column_check IN critical_columns
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE (table_schema || '.' || table_name = column_check.table_name
                   OR (table_schema = 'public' AND table_name = column_check.table_name))
            AND column_name = column_check.column_name
        ) THEN
            missing_columns := array_append(missing_columns, 
                column_check.table_name || '.' || column_check.column_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_columns, 1) > 0 THEN
        RAISE EXCEPTION 'Missing critical columns: %', array_to_string(missing_columns, ', ');
    ELSE
        RAISE NOTICE 'All critical columns exist ✓';
    END IF;
END $$;

-- Check that foreign key relationships exist
DO $$
DECLARE
    missing_fks TEXT[] := ARRAY[]::TEXT[];
    fk_check RECORD;
    required_fks CURSOR FOR
        SELECT 'ptvuser.portfolios' as table_name, 'user_id' as column_name, 'user' as ref_table, 'id' as ref_column
        UNION ALL SELECT 'ptvuser.transactions', 'portfolio_id', 'ptvuser.portfolios', 'id'
        UNION ALL SELECT 'ptvuser.asset_prices', 'asset_id', 'ptvuser.asset', 'asset_id'
        UNION ALL SELECT 'ptvuser.asset_metrics', 'asset_id', 'ptvuser.asset', 'asset_id'
        UNION ALL SELECT 'ptvuser.dividend', 'asset_id', 'ptvuser.asset', 'asset_id'
        UNION ALL SELECT 'ptvuser.profile_pictures', 'user_id', 'user', 'id';
BEGIN
    FOR fk_check IN required_fks
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage ccu 
                ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND (tc.table_schema || '.' || tc.table_name = fk_check.table_name
                 OR (tc.table_schema = 'public' AND tc.table_name = fk_check.table_name))
            AND kcu.column_name = fk_check.column_name
        ) THEN
            missing_fks := array_append(missing_fks, 
                fk_check.table_name || '.' || fk_check.column_name || ' -> ' || 
                fk_check.ref_table || '.' || fk_check.ref_column);
        END IF;
    END LOOP;
    
    IF array_length(missing_fks, 1) > 0 THEN
        RAISE WARNING 'Missing foreign keys: %', array_to_string(missing_fks, ', ');
    ELSE
        RAISE NOTICE 'All critical foreign keys exist ✓';
    END IF;
END $$;

-- Summary
SELECT 'Database schema validation completed successfully!' as status;
