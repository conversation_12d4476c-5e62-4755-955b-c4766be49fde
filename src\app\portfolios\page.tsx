import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { PortfoliosClient } from "@/components/portfolios/portfolios-client";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Portofoliile mele - Portavio",
  description:
    "Urmărește-ți investițiile și performanța portofoliilor cu Portavio. Vizualizează poziții, tranzacții și metrici detaliate.",
  keywords: [
    "portofolii",
    "investiții",
    "urmărire",
    "performanță",
    "poziții",
    "tranzacții",
    "portavio",
    "management financiar",
  ],
  openGraph: {
    title: "Portofoliile mele - Portavio",
    description:
      "Urmărește-ți investițiile și performanța portofoliilor cu Portavio.",
    type: "website",
  },
};

export default async function PortfoliosPage() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    redirect("/auth/signin");
  }

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-7xl">
      <PortfoliosClient />
    </div>
  );
}
