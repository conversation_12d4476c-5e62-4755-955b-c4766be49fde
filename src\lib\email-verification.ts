import { auth } from "./auth";
import { sendEmail } from "./sendEmail";
import { APIError } from "better-auth/api";
import { hasuraQuery } from "@/utils/db/hasura";
import { sendWelcomeEmailIfNeeded } from "@/utils/db/welcome-email-queries";

export interface VerificationResult {
  success: boolean;
  message: string;
  userEmail?: string;
  userName?: string;
  welcomeEmailSent?: boolean;
  alreadyVerified?: boolean; // Flag to indicate if email was already verified
  isNewVerification?: boolean; // Flag to indicate if this was a new verification
  error?: string;
}

/**
 * Check if a user's email is already verified
 * @param userEmail Email address to check
 * @returns boolean indicating if email is verified
 */
async function checkEmailVerificationStatus(
  userEmail: string
): Promise<boolean> {
  try {
    const query = `
      query CheckEmailVerification($email: String!) {
        ptvuser_user(where: {email: {_eq: $email}}, limit: 1) {
          id
          email
          emailVerified
        }
      }
    `;

    const result = await hasuraQuery<{
      ptvuser_user: Array<{
        id: string;
        email: string;
        emailVerified: boolean;
      }>;
    }>(query, { variables: { email: userEmail } });

    const user = result.ptvuser_user?.[0];
    return user?.emailVerified || false;
  } catch (error) {
    console.error("Error checking email verification status:", error);
    // If we can't check the status, assume it's not verified to be safe
    return false;
  }
}

/**
 * Check if a user exists by email address
 * @param userEmail Email address to check
 * @returns boolean indicating if user exists
 */
export async function checkUserExists(userEmail: string): Promise<boolean> {
  try {
    const query = `
      query CheckUserExists($email: String!) {
        ptvuser_user(where: {email: {_eq: $email}}, limit: 1) {
          id
          email
        }
      }
    `;

    const result = await hasuraQuery<{
      ptvuser_user: Array<{
        id: string;
        email: string;
      }>;
    }>(query, { variables: { email: userEmail } });

    return result.ptvuser_user?.length > 0;
  } catch (error) {
    console.error("Error checking if user exists:", error);
    return false;
  }
}

/**
 * Send verification email with professional Romanian template
 * @param userEmail Email address to send verification to
 * @param userName User's display name (optional)
 * @param verificationToken JWT token for email verification
 */
export async function sendVerificationEmail(
  userEmail: string,
  userName: string,
  verificationToken: string
): Promise<void> {
  const baseURL = process.env.BETTER_AUTH_URL || "http://localhost:3000";
  const verificationUrl = `${baseURL}/verify-email?token=${verificationToken}`;

  const emailSubject = "Verifică adresa de email - Portavio";
  const emailHtml = `
    <!DOCTYPE html>
    <html lang="ro">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verifică adresa de email - Portavio</title>
      
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f8fafc;">
        <tr>
          <td align="center" style="padding: 40px 20px;">
            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="max-width: 600px; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
              <!-- Header with Brand -->
              <tr>
                <td align="center" style="padding: 40px 40px 30px 40px; background-color: #ff7f50; border-radius: 12px 12px 0 0;">
                  <div style="display:  inline-block; padding: 16px 24px; background-color: rgba(255, 255, 255, 0.2); border-radius: 8px; margin-bottom: 20px; border: 2px solid rgba(255, 255, 255, 0.3);">
                    <h1 style="color: #ffffff !important; font-size: 24px; font-weight: 700; margin: 0; text-align: center; letter-spacing: 1px; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">PORTAVIO</h1>
                  </div>
                  <h2 style="color: #ffffff !important; font-size: 28px; font-weight: 700; margin: 0; text-align: center; line-height: 1.2; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">Verifică adresa de email</h2>
                  <p style="color: #ffffff !important; font-size: 16px; margin: 8px 0 0 0; text-align: center; opacity: 0.95;">Pentru a activa contul Portavio</p>
                </td>
              </tr>

              <!-- Main Content -->
              <tr>
                <td style="padding: 40px;">
                  <h2 style="color: #1a202c; font-size: 24px; font-weight: 600; margin: 0 0 20px 0; text-align: left;">Salut${
                    userName ? `, ${userName}` : ""
                  }!</h2>

                  <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                    Îți mulțumim că te-ai înregistrat pe <strong>Portavio</strong>! Pentru a-ți activa contul și a începe să îți urmărești portofoliul, te rugăm să verifici adresa de email făcând clic pe butonul de mai jos.
                  </p>

                  <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 0 0 30px 0;">
                    ⏰ Acest link va expira <strong>într-o oră</strong> din motive de securitate.
                  </p>

                  <!-- CTA Button -->
                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                    <tr>
                      <td align="center" style="padding: 20px 0;">
                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                          <tr>
                            <td align="center" style="background-color: #ff7f50; border-radius: 6px; padding: 14px 28px;">
                              <a href="${verificationUrl}" style="color: white; font-size: 16px; font-weight: bold; text-decoration: none; display: block;">
                                Verifică Email-ul
                              </a>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>

                  <!-- Security Notice -->
                  <div style="background-color: #fef5e7; border-left: 4px solid #f6ad55; padding: 16px; border-radius: 6px; margin: 30px 0;">
                    <p style="color: #744210; font-size: 14px; margin: 0; line-height: 1.5;">
                      <strong>🔒 Notă de securitate:</strong> Dacă nu te-ai înregistrat pe Portavio, poți ignora acest email în siguranță. Contul nu va fi creat fără verificarea email-ului.
                    </p>
                  </div>

                  <!-- Alternative Link -->
                  <div style="background-color: #f7fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <p style="color: #4a5568; font-size: 13px; margin: 0 0 10px 0; text-align: center;">
                      <strong>Butonul nu funcționează?</strong> Copiază și lipește acest link în browser:
                    </p>
                    <p style="color: #ff7f50; font-size: 12px; margin: 0; text-align: center; word-break: break-all;">
                      <a href="${verificationUrl}" style="color: #ff7f50; text-decoration: underline;">${verificationUrl}</a>
                    </p>
                  </div>
                </td>
              </tr>

              <!-- Footer -->
              <tr>
                <td style="padding: 30px 40px; background-color: #f7fafc; border-radius: 0 0 12px 12px; border-top: 1px solid #e2e8f0;">
                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                    <tr>
                      <td align="center">
                        <p style="color: #718096; font-size: 12px; margin: 0 0 8px 0; text-align: center;">
                          © ${new Date().getFullYear()} <strong>Portavio</strong>. Toate drepturile rezervate.
                        </p>
                        <p style="color: #a0aec0; font-size: 11px; margin: 0; text-align: center;">
                          Platforma ta de urmărire a portofoliului de investiții
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </body>
    </html>
  `;

  await sendEmail({
    to: userEmail,
    subject: emailSubject,
    html: emailHtml,
  });
}

/**
 * Send welcome email after successful verification
 * @param userEmail Email address to send welcome email to
 * @param userName User's display name (optional)
 */
export async function sendWelcomeEmail(
  userEmail: string,
  userName?: string
): Promise<void> {
  const welcomeSubject = "Bun venit la Portavio!";
  const welcomeHtml = `
    <!DOCTYPE html>
    <html lang="ro">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Bun venit la Portavio!</title>
      <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <![endif]-->
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f8fafc;">
        <tr>
          <td align="center" style="padding: 40px 20px;">
            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="max-width: 600px; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
              <!-- Header with Brand -->
              <tr>
                <td align="center" style="padding: 40px 40px 30px 40px; background-color: #10b981;">
                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                    <tr>
                      <td align="center">
                        <div style="display: inline-block; padding: 16px 24px; background-color: rgba(255, 255, 255, 0.2); border-radius: 8px; margin-bottom: 20px; border: 2px solid rgba(255, 255, 255, 0.3);">
                          <h1 style="color: white; font-size: 24px; font-weight: 700; margin: 0; text-align: center; letter-spacing: 1px;">PORTAVIO</h1>
                        </div>
                        <h2 style="color: white; font-size: 32px; font-weight: 700; margin: 0; text-align: center; line-height: 1.2;">🎉 Bun venit la Portavio!</h2>
                        <p style="color: white; font-size: 16px; margin: 8px 0 0 0; text-align: center;">Contul tău a fost activat cu succes</p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>

              <!-- Main Content -->
              <tr>
                <td style="padding: 40px;">
                  <h2 style="color: #1a202c; font-size: 24px; font-weight: 600; margin: 0 0 20px 0; text-align: left;">Salut${
                    userName ? `, ${userName}` : ""
                  }!</h2>

                  <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                    🎊 <strong>Felicitări!</strong> Te-ai alăturat cu succes platformei <strong>Portavio</strong>! Email-ul tău a fost verificat și contul tău este acum complet activ.
                  </p>

                  <div style="background-color: #f0fff4; border-left: 4px solid #10b981; padding: 20px; border-radius: 6px; margin: 25px 0;">
                    <h3 style="color: #065f46; font-size: 18px; font-weight: 600; margin: 0 0 12px 0;">Ce poți face acum:</h3>
                    <ul style="color: #047857; font-size: 15px; line-height: 1.6; margin: 0; padding-left: 20px;">
                      <li style="margin-bottom: 8px;">📊 Urmărește portofoliul tău de investiții în timp real</li>
                      <li style="margin-bottom: 8px;">📈 Analizează performanța investițiilor tale</li>
                      <li style="margin-bottom: 8px;">🔔 Primește notificări pentru schimbări importante</li>
                      <li style="margin-bottom: 0;">⚙️ Personalizează setările contului tău</li>
                    </ul>
                  </div>

                  <!-- CTA Button -->
                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                    <tr>
                      <td align="center" style="padding: 25px 0;">
                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                          <tr>
                            <td align="center" style="background-color: #ff7f50; border-radius: 6px; padding: 14px 28px;">
                              <a href="${
                                process.env.BETTER_AUTH_URL ||
                                "http://localhost:3000"
                              }/profile" style="color: white; font-size: 16px; font-weight: bold; text-decoration: none; display: block;">
                                Accesează Profilul
                              </a>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>

                  <!-- Support Section -->
                  <div style="background-color: #f7fafc; padding: 20px; border-radius: 8px; margin: 25px 0;">
                    <h3 style="color: #2d3748; font-size: 16px; font-weight: 600; margin: 0 0 10px 0; text-align: center;">💬 Ai nevoie de ajutor?</h3>
                    <p style="color: #4a5568; font-size: 14px; margin: 0; text-align: center; line-height: 1.5;">
                      Echipa noastră de suport este aici să te ajute! Nu ezita să ne contactezi la
                      <a href="mailto:<EMAIL>" style="color: #ff7f50; text-decoration: none; font-weight: 600;"><EMAIL></a>
                    </p>
                  </div>

                  <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 20px 0 0 0; text-align: center;">
                    Îți mulțumim că ai ales Portavio pentru gestionarea portofoliului tău de investiții! 🙏
                  </p>
                </td>
              </tr>

              <!-- Footer -->
              <tr>
                <td style="padding: 30px 40px; background-color: #f7fafc; border-radius: 0 0 12px 12px; border-top: 1px solid #e2e8f0;">
                  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                    <tr>
                      <td align="center">
                        <p style="color: #718096; font-size: 12px; margin: 0 0 8px 0; text-align: center;">
                          © ${new Date().getFullYear()} <strong>Portavio</strong>. Toate drepturile rezervate.
                        </p>
                        <p style="color: #a0aec0; font-size: 11px; margin: 0; text-align: center;">
                          Platforma ta de urmărire a portofoliului de investiții
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </body>
    </html>
  `;

  await sendEmail({
    to: userEmail,
    subject: welcomeSubject,
    html: welcomeHtml,
  });
}

/**
 * Verify email token and handle welcome email sending
 * @param token JWT verification token
 * @returns VerificationResult with success status and details
 */
export async function verifyEmailToken(
  token: string
): Promise<VerificationResult> {
  let userEmail = "";

  // First, decode the token to get the user email
  try {
    const tokenPayload = JSON.parse(atob(token.split(".")[1]));
    userEmail = tokenPayload.email || "";
  } catch (decodeError) {
    console.error("Failed to decode token:", decodeError);
  }

  // Check if email was already verified BEFORE calling the verification API
  let wasAlreadyVerified = false;
  if (userEmail) {
    try {
      wasAlreadyVerified = await checkEmailVerificationStatus(userEmail);
    } catch (error) {
      console.error("Error checking verification status:", error);
    }
  }

  try {
    // Verify email using better-auth API
    const result: any = await auth.api.verifyEmail({
      query: { token },
    });

    // Check if verification was successful
    // The API returns { status: true } for success, not a user object
    if (!result || !result.status) {
      return {
        success: false,
        message: "Token-ul de verificare este invalid sau a expirat.",
        error: "INVALID_TOKEN",
      };
    }

    // If we couldn't decode the token earlier, handle it here
    if (!userEmail) {
      return {
        success: true,
        message: "Email-ul a fost verificat cu succes! Bun venit la Portavio!",
        welcomeEmailSent: false,
        error: "TOKEN_DECODE_FAILED",
      };
    }

    // Determine if this is a new verification or already verified
    const isNewVerification = !wasAlreadyVerified;

    if (isNewVerification) {
      // This is a new verification, send welcome email using database-driven approach
      try {
        // Get user ID by email to use the new welcome email system
        const userQuery = `
          query GetUserByEmail($email: String!) {
            ptvuser_user(where: {email: {_eq: $email}}, limit: 1) {
              id
              email
              name
            }
          }
        `;

        const userResult = await hasuraQuery<{
          ptvuser_user: Array<{
            id: string;
            email: string;
            name?: string;
          }>;
        }>(userQuery, { variables: { email: userEmail } });

        const user = userResult.ptvuser_user?.[0];

        if (!user) {
          // User not found, fallback to old method
          await sendWelcomeEmail(userEmail);

          return {
            success: true,
            message:
              "Email-ul a fost verificat cu succes! Bun venit la Portavio!",
            userEmail: userEmail,
            welcomeEmailSent: true,
            alreadyVerified: false,
            isNewVerification: true,
            error: "USER_NOT_FOUND_FALLBACK",
          };
        }

        // Use the new database-driven welcome email system
        const welcomeResult = await sendWelcomeEmailIfNeeded(
          user.id,
          user.email,
          user.name
        );

        return {
          success: true,
          message:
            "Email-ul a fost verificat cu succes! Bun venit la Portavio!",
          userEmail: userEmail,
          welcomeEmailSent: welcomeResult.emailSent,
          alreadyVerified: false,
          isNewVerification: true,
        };
      } catch (emailError) {
        console.error("Failed to send welcome email:", emailError);

        // Verification succeeded but welcome email failed
        return {
          success: true,
          message:
            "Email-ul a fost verificat cu succes! Bun venit la Portavio!",
          userEmail: userEmail,
          welcomeEmailSent: false,
          alreadyVerified: false,
          isNewVerification: true,
          error: "WELCOME_EMAIL_FAILED",
        };
      }
    } else {
      // Email was already verified, don't send welcome email
      return {
        success: true,
        message: "Email-ul este deja verificat. Bun venit înapoi la Portavio!",
        userEmail: userEmail,
        welcomeEmailSent: false,
        alreadyVerified: true,
        isNewVerification: false,
      };
    }
  } catch (error) {
    console.error("Email verification error:", error);

    if (error instanceof APIError) {
      // Handle specific better-auth errors
      switch (error.status) {
        case 400:
          return {
            success: false,
            message: "Token-ul de verificare este invalid.",
            error: "INVALID_TOKEN",
          };
        case 404:
          return {
            success: false,
            message: "Token-ul de verificare nu a fost găsit sau a expirat.",
            error: "TOKEN_NOT_FOUND",
          };
        case 410:
          return {
            success: false,
            message:
              "Token-ul de verificare a expirat. Vă rugăm să solicitați un nou email de verificare.",
            error: "TOKEN_EXPIRED",
          };
        default:
          return {
            success: false,
            message:
              "S-a produs o eroare la verificarea email-ului. Vă rugăm să încercați din nou.",
            error: "VERIFICATION_ERROR",
          };
      }
    }

    return {
      success: false,
      message:
        "S-a produs o eroare neașteptată. Vă rugăm să contactați suportul tehnic.",
      error: "UNEXPECTED_ERROR",
    };
  }
}
