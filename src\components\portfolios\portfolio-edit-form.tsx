"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  PortfolioEditFormData,
  portfolioEditSchema,
  getDefaultPortfolioEditFormData,
  portfolioStatusOptions,
  booleanToString,
  stringToBoolean,
} from "@/lib/portfolio-schemas";
import { Portfolio } from "@/utils/db/portfolio-queries";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, Save, Loader2 } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

interface PortfolioEditFormProps {
  portfolio: Portfolio;
  onSubmit: (data: PortfolioEditFormData) => Promise<void>;
  isLoading?: boolean;
}

export function PortfolioEditForm({
  portfolio,
  onSubmit,
  isLoading = false,
}: PortfolioEditFormProps) {
  const [error, setError] = useState<string | null>(null);

  const form = useForm<PortfolioEditFormData>({
    resolver: zodResolver(portfolioEditSchema),
    defaultValues: getDefaultPortfolioEditFormData(portfolio),
  });

  const handleFormSubmit = async (data: PortfolioEditFormData) => {
    setError(null);

    try {
      await onSubmit(data);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  const isDirty = form.formState.isDirty;
  const isSubmitting = form.formState.isSubmitting;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Save className="h-5 w-5" />
          Editare Portofoliu
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleFormSubmit)}
            className="space-y-6"
          >
            {error && (
              <div className="rounded-md bg-red-50 p-3 text-sm text-red-600 dark:text-red-400 dark:bg-red-900/20 flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                {error}
              </div>
            )}

            {/* Portfolio Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Numele Portofoliului *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Introdu numele portofoliului..."
                      disabled={isLoading || isSubmitting}
                      className={
                        form.formState.errors.name
                          ? "border-red-500 dark:border-red-400"
                          : ""
                      }
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Numele portofoliului trebuie să fie unic și descriptiv.
                  </FormDescription>
                  <FormMessage className="text-red-600 dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Portfolio Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrierea Portofoliului</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Introdu o descriere pentru portofoliu (opțional)..."
                      disabled={isLoading || isSubmitting}
                      className={
                        form.formState.errors.description
                          ? "border-red-500 dark:border-red-400"
                          : ""
                      }
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    O descriere scurtă care să explice scopul acestui
                    portofoliu.
                  </FormDescription>
                  <FormMessage className="text-red-600 dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Portfolio Status */}
            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Statusul Portofoliului *</FormLabel>
                  <Select
                    onValueChange={(value) =>
                      field.onChange(stringToBoolean(value))
                    }
                    value={booleanToString(field.value)}
                    disabled={isLoading || isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger
                        className={
                          form.formState.errors.is_active
                            ? "border-red-500 dark:border-red-400"
                            : ""
                        }
                      >
                        <SelectValue placeholder="Selectează statusul portofoliului" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {portfolioStatusOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex flex-row items-center gap-2 hover:text-white">
                            <span className="font-medium">{option.label}</span>-
                            <span className="text-xs text-muted-foreground hover:text-white">
                              {option.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Portofoliile inactive sunt ascunse din lista principală.
                  </FormDescription>
                  <FormMessage className="text-red-600 dark:text-red-400" />
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="submit"
                disabled={isLoading || isSubmitting || !isDirty}
                className="gap-2"
              >
                {isSubmitting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                {isSubmitting ? "Se salvează..." : "Salvează Modificările"}
              </Button>
            </div>

            {!isDirty && (
              <p className="text-sm text-muted-foreground text-center">
                Nu există modificări de salvat.
              </p>
            )}
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
