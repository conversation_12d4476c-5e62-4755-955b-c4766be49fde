import { PortfolioGridSkeleton } from "@/components/portfolios/portfolio-skeleton";

export default function PortfoliosLoading() {
  return (
    <div className="container mx-auto px-4 py-8 min-h-screen">
      <div className="space-y-6">
        {/* Header skeleton */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 w-48 bg-muted animate-pulse rounded" />
            <div className="h-4 w-96 bg-muted animate-pulse rounded" />
          </div>
          <div className="h-10 w-32 bg-muted animate-pulse rounded" />
        </div>

        {/* Stats skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="bg-card border rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="h-9 w-9 bg-muted animate-pulse rounded-md" />
                <div className="space-y-2">
                  <div className="h-6 w-8 bg-muted animate-pulse rounded" />
                  <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Grid skeleton */}
        <PortfolioGridSkeleton count={6} />
      </div>
    </div>
  );
}
