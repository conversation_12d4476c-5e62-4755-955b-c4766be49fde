import { auth } from "@/lib/auth";
import {
  getPortfolioById,
  updatePortfolio,
} from "@/utils/db/portfolio-queries";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

// POST /api/portfolios/[id]/reactivate - Reactivate a portfolio
export async function POST(
  _: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const { id } = await params;
    const portfolioId = id;

    if (!portfolioId) {
      return NextResponse.json(
        { error: "ID-ul portofoliului este obligatoriu" },
        { status: 400 }
      );
    }

    const existingPortfolio = await getPortfolioById(portfolioId);

    if (!existingPortfolio) {
      return NextResponse.json(
        { error: "Portofoliul nu a fost găsit" },
        { status: 404 }
      );
    }

    if (existingPortfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să modifici acest portofoliu" },
        { status: 403 }
      );
    }

    if (existingPortfolio.is_active) {
      return NextResponse.json(
        { error: "Portofoliul este deja activ" },
        { status: 400 }
      );
    }

    const updatedPortfolio = await updatePortfolio(portfolioId, {
      is_active: true,
    });

    return NextResponse.json({
      portfolio: updatedPortfolio,
      message: "Portofoliul a fost reactivat cu succes",
    });
  } catch (error) {
    console.error("Error reactivating portfolio:", error);

    if (error instanceof Error) {
      if (error.message === "Portofoliul nu a fost găsit") {
        return NextResponse.json({ error: error.message }, { status: 404 });
      }
    }

    return NextResponse.json(
      { error: "Nu s-a putut reactiva portofoliul" },
      { status: 500 }
    );
  }
}
