"use client";

import { CompaniesDataTable } from "@/components/dashboard/companies-data-table";
import { CurrencySelector } from "@/components/dashboard/currency-selector";
import { DividendsCard } from "@/components/dashboard/dividends-card";
import { PortfolioCompositionChart } from "@/components/dashboard/portfolio-composition-chart";
import { PortfolioMetricsCard } from "@/components/dashboard/portfolio-metrics-card";
import { PortfolioMultiSelect } from "@/components/dashboard/portfolio-multi-select";
import { PortfolioPerformanceChart } from "@/components/dashboard/portfolio-performance-chart";
import { PageLoading } from "@/components/loading-states";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useDashboardPersistence } from "@/hooks/use-dashboard-persistence";
import { useAuth } from "@/hooks/use-auth";
import { Info } from "lucide-react";

export default function DashboardClient() {
  const { user } = useAuth();

  const {
    selectedPortfolios,
    displayCurrency,
    setSelectedPortfolios,
    setDisplayCurrency,
    isLoading: isPreferencesLoading,
  } = useDashboardPersistence({
    userId: user?.id || "",
  });

  if (isPreferencesLoading) {
    return <PageLoading message="Se încarcă preferințele dashboard-ului..." />;
  }

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-7xl">
      {/* Page Header */}
      <div className="flex flex-wrap gap-12 items-center mb-8 justify-between">
        <div>
          <h1 className="text-5xl font-bold text-gray-900 dark:text-gray-50 mb-2">
            Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 flex items-center gap-2">
            Analiză completă a portofoliilor tale de investiții
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 inline-block ml-1" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-[220px]">
                  Toate valorile afișate sunt estimative și sunt rotunjite
                  pentru claritate. Pot varia ușor față de valorile reale din
                  cauza cursurilor de schimb și a rotunjirilor.
                </p>
              </TooltipContent>
            </Tooltip>
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          <div>
            <h3 className="text-md font-medium mb-2 text-gray-900 dark:text-gray-50">
              Filtrează după portofolii:
            </h3>
            <PortfolioMultiSelect
              selectedPortfolios={selectedPortfolios}
              onSelectionChange={setSelectedPortfolios}
            />
          </div>

          <div>
            <h3 className="text-md font-medium mb-2 text-gray-900 dark:text-gray-50">
              Moneda de afișare:
            </h3>
            <CurrencySelector
              selectedCurrency={displayCurrency}
              onCurrencyChange={setDisplayCurrency}
            />
          </div>
        </div>
      </div>

      {/* Dashboard Grid Layout */}
      <div className="grid gap-6">
        {/* Row 1: 2 cards (left wider than right) */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Card 1 - Performance Overview (2/3 width) */}
          <PortfolioPerformanceChart
            selectedPortfolios={selectedPortfolios}
            displayCurrency={displayCurrency}
          />

          {/* Card 2 - Portfolio Composition (1/3 width) */}
          <div className="lg:col-span-1">
            <PortfolioCompositionChart
              selectedPortfolios={selectedPortfolios}
              displayCurrency={displayCurrency}
            />
          </div>
        </div>

        {/* Row 2: 2 cards (left wider than right) */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Card 3 - Dividends (2/3 width) */}
          <DividendsCard
            selectedPortfolios={selectedPortfolios}
            displayCurrency={displayCurrency}
          />

          {/* Card 4 - Portfolio Metrics (1/3 width) */}
          <div className="lg:col-span-1">
            <PortfolioMetricsCard
              selectedPortfolios={selectedPortfolios}
              displayCurrency={displayCurrency}
            />
          </div>
        </div>

        {/* Row 3: 1 full-width card */}
        <div className="grid grid-cols-1 gap-6">
          {/* Card 5 - Companies Data Table */}
          <CompaniesDataTable
            selectedPortfolios={selectedPortfolios}
            displayCurrency={displayCurrency}
          />
        </div>
      </div>
    </div>
  );
}
