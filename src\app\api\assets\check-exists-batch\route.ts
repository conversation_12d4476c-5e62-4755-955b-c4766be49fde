import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { checkMultipleAssetsExist } from "@/lib/asset-existence-check";

// Validation schema for request body
const checkExistsBatchSchema = z.object({
  tickers: z
    .array(z.string().min(1).max(20))
    .min(1, "Cel puțin un simbol este necesar")
    .max(50, "Nu se pot verifica mai mult de 50 de simboluri odată"),
});

// Response types
interface CheckExistsBatchSuccessResponse {
  success: true;
  results: Array<{
    ticker: string;
    exists: boolean;
  }>;
  count: number;
}

interface CheckExistsBatchErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

// POST /api/assets/check-exists-batch - Check if multiple tickers exist in the database
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    let requestData;
    try {
      const body = await request.json();
      requestData = checkExistsBatchSchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: "Datele cererii nu sunt valide",
            code: "VALIDATION_ERROR",
            details: error.errors.map((err) => ({
              field: err.path.join("."),
              message: err.message,
            })),
          } as CheckExistsBatchErrorResponse,
          { status: 400 }
        );
      }
      throw error;
    }

    console.log(`Batch checking asset existence for ${requestData.tickers.length} tickers`);

    // Check if assets exist
    const existenceMap = await checkMultipleAssetsExist(requestData.tickers);

    // Convert map to array format
    const results = requestData.tickers.map((ticker) => ({
      ticker: ticker.toUpperCase(),
      exists: existenceMap.get(ticker.toUpperCase()) || false,
    }));

    const successResponse: CheckExistsBatchSuccessResponse = {
      success: true,
      results,
      count: results.length,
    };

    return NextResponse.json(successResponse);

  } catch (error) {
    console.error("Error in batch asset existence check API:", error);

    return NextResponse.json(
      {
        success: false,
        error: "A apărut o eroare la verificarea existenței activelor",
        code: "INTERNAL_ERROR",
        details: error instanceof Error ? error.message : String(error),
      } as CheckExistsBatchErrorResponse,
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește POST pentru verificarea în lot",
      code: "METHOD_NOT_ALLOWED",
    } as CheckExistsBatchErrorResponse,
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește POST pentru verificarea în lot",
      code: "METHOD_NOT_ALLOWED",
    } as CheckExistsBatchErrorResponse,
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește POST pentru verificarea în lot",
      code: "METHOD_NOT_ALLOWED",
    } as CheckExistsBatchErrorResponse,
    { status: 405 }
  );
}
