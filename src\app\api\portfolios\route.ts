import { auth } from "@/lib/auth";
import {
  create<PERSON>ortfolio,
  getUserPortfolios,
} from "@/utils/db/portfolio-queries";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

// GET /api/portfolios - Get user's portfolios
export async function GET() {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const portfolios = await getUserPortfolios(session.user.id);

    return NextResponse.json({
      portfolios,
      count: portfolios.length,
    });
  } catch (error) {
    console.error("Error fetching portfolios:", error);
    return NextResponse.json(
      { error: "Nu s-au putut încărca portofoliile" },
      { status: 500 }
    );
  }
}

// POST /api/portfolios - Create a new portfolio
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate request body
    const createPortfolioSchema = z.object({
      name: z
        .string()
        .min(1, "Numele portofoliului este obligatoriu")
        .max(255, "Numele nu poate avea mai mult de 255 de caractere"),
      description: z
        .string()
        .max(1000, "Descrierea nu poate avea mai mult de 1000 de caractere")
        .optional(),
    });

    const validatedData = createPortfolioSchema.parse(body);

    const portfolio = await createPortfolio(
      session.user.id,
      validatedData.name,
      validatedData.description
    );

    return NextResponse.json({
      portfolio,
      message: "Portofoliul a fost creat cu succes",
    });
  } catch (error) {
    console.error("Error creating portfolio:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Date invalide", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Nu s-a putut crea portofoliul" },
      { status: 500 }
    );
  }
}
