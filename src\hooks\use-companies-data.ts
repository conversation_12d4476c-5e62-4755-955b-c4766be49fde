"use client";

import { useQuery } from "@tanstack/react-query";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import { CompanyData } from "@/utils/db/dashboard-queries";

// Query keys for companies data
export const companiesDataKeys = {
  all: ["companies"] as const,
  lists: () => [...companiesDataKeys.all, "list"] as const,
  list: (portfolioIds: string[], displayCurrency: SupportedCurrency) =>
    [...companiesDataKeys.lists(), { portfolioIds, displayCurrency }] as const,
};

// Fetch function for companies data
async function fetchCompaniesData(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency
): Promise<CompanyData[]> {
  try {
    const params = new URLSearchParams({
      portfolioIds: portfolioIds.join(","),
      displayCurrency,
    });

    const response = await fetch(`/api/dashboard/companies?${params}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.error || "Nu s-au putut încărca datele companiilor"
      );
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error("Error fetching companies data:", error);
    throw new Error(
      error instanceof Error
        ? error.message
        : "Nu s-au putut încărca datele companiilor"
    );
  }
}

// Hook for fetching companies data
export function useCompaniesData(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
) {
  return useQuery<CompanyData[]>({
    queryKey: companiesDataKeys.list(portfolioIds, displayCurrency),
    queryFn: () => fetchCompaniesData(portfolioIds, displayCurrency),
    enabled: portfolioIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error instanceof Error && error.message.includes("autentificat")) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
}
