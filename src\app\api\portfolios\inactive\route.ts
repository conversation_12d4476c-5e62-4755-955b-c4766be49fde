import { auth } from "@/lib/auth";
import { getUserInactivePortfolios } from "@/utils/db/portfolio-queries";
import { headers } from "next/headers";
import { NextResponse } from "next/server";

// GET /api/portfolios/inactive - Get user's inactive portfolios
export async function GET() {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const portfolios = await getUserInactivePortfolios(session.user.id);

    return NextResponse.json({
      portfolios,
      count: portfolios.length,
      message: "Portofoliile inactive au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error fetching inactive portfolios:", error);
    return NextResponse.json(
      { error: "Nu s-au putut încărca portofoliile inactive" },
      { status: 500 }
    );
  }
}
