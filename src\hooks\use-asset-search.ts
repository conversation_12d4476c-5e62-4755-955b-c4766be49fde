"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { AssetSearchResult } from "@/utils/db/asset-queries";
import { ProcessedSearchResult } from "@/lib/eodhd-search-client";

interface UseAssetSearchOptions {
  debounceMs?: number;
  minSearchLength?: number;
  maxResults?: number;
  prioritizeEODHD?: boolean; // Whether to prioritize EODHD search over database search
  databaseOnly?: boolean; // Whether to search only in database (no EODHD calls)
}

// Combined search result type that can represent both database and EODHD results
export interface CombinedSearchResult {
  // Common fields
  ticker: string;
  name: string;
  description: string;

  // Database-specific fields (when source is 'database')
  asset_id?: number;
  company?: string;
  logo_url?: string;

  // EODHD-specific fields (when source is 'eodhd')
  exchange?: string;
  type?: string;
  country?: string;
  currency?: string;
  isin?: string;
  previousClose?: number;
  previousCloseDate?: string;
  displayTicker?: string;

  // Metadata
  source: "database" | "eodhd";
  isExistingAsset: boolean; // Whether this ticker exists in our database
}

interface UseAssetSearchReturn {
  assets: CombinedSearchResult[];
  loading: boolean;
  error: string | null;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  clearSearch: () => void;
  searchSource: "database" | "eodhd" | "combined" | null;
}

export function useAssetSearch(
  options: UseAssetSearchOptions = {}
): UseAssetSearchReturn {
  const {
    debounceMs = 300,
    minSearchLength = 1,
    maxResults = 15,
    prioritizeEODHD = true, // Default to prioritizing EODHD search
    databaseOnly = false, // Default to allowing EODHD search
  } = options;

  const [assets, setAssets] = useState<CombinedSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchSource, setSearchSource] = useState<
    "database" | "eodhd" | "combined" | null
  >(null);

  // Use ref to track the current request to avoid race conditions
  const currentRequestRef = useRef<AbortController | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Helper function to convert database results to combined format
  const convertDatabaseResults = useCallback(
    (dbResults: AssetSearchResult[]): CombinedSearchResult[] => {
      return dbResults.map((asset) => ({
        ticker: asset.ticker,
        name: asset.name,
        description: `${asset.name} • ${asset.company}`,
        asset_id: asset.asset_id,
        company: asset.company,
        logo_url: asset.logo_url,
        currency: asset.currency?.code,
        source: "database" as const,
        isExistingAsset: true,
      }));
    },
    []
  );

  // Helper function to convert EODHD results to combined format
  const convertEODHDResults = useCallback(
    (
      eodhResults: ProcessedSearchResult[],
      existingTickers: Set<string>
    ): CombinedSearchResult[] => {
      return eodhResults.map((result) => ({
        ticker: result.ticker,
        name: result.name,
        description: result.description,
        exchange: result.exchange,
        type: result.type,
        country: result.country,
        currency: result.currency,
        isin: result.isin,
        previousClose: result.previousClose,
        previousCloseDate: result.previousCloseDate,
        displayTicker: result.displayTicker,
        source: "eodhd" as const,
        isExistingAsset: existingTickers.has(
          result.displayTicker.toUpperCase()
        ),
      }));
    },
    []
  );

  // Function to search database assets
  const searchDatabase = useCallback(
    async (term: string, signal: AbortSignal): Promise<AssetSearchResult[]> => {
      const searchParams = new URLSearchParams({
        q: term.trim(),
        limit: maxResults.toString(),
      });

      const response = await fetch(
        `/api/assets/search?${searchParams.toString()}`,
        {
          signal,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "A apărut o eroare la căutarea în baza de date"
        );
      }

      const data = await response.json();
      return data.data || [];
    },
    [maxResults]
  );

  // Function to search EODHD
  const searchEODHD = useCallback(
    async (
      term: string,
      signal: AbortSignal
    ): Promise<ProcessedSearchResult[]> => {
      const searchParams = new URLSearchParams({
        q: term.trim(),
        limit: maxResults.toString(),
      });

      const response = await fetch(
        `/api/search/eodhd?${searchParams.toString()}`,
        {
          signal,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "A apărut o eroare la căutarea EODHD"
        );
      }

      const data = await response.json();
      return data.data || [];
    },
    [maxResults]
  );

  // Function to cache search results in background (non-blocking)
  const cacheSearchResultsInBackground = useCallback(
    async (eodhResults: ProcessedSearchResult[]) => {
      // Don't block the UI - run this in background
      try {
        if (eodhResults.length === 0) {
          return;
        }

        // Convert EODHD results to cache format
        const cacheData = eodhResults.map((result) => ({
          code: result.ticker,
          exchange: result.exchange,
          name: result.name,
          type: result.type,
          country: result.country,
          currency: result.currency,
          isin: result.isin || null,
        }));

        // Cache in background without awaiting
        fetch("/api/search/cache", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            results: cacheData,
          }),
        }).catch((error) => {
          // Silently log cache errors - don't affect user experience
          console.warn("Background caching failed:", error);
        });
      } catch (error) {
        // Silently log cache errors - don't affect user experience
        console.warn("Background caching preparation failed:", error);
      }
    },
    []
  );

  // Main debounced search function that combines both sources
  const debouncedSearch = useCallback(
    async (term: string) => {
      // Clear any existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // If search term is too short, clear results
      if (term.length < minSearchLength) {
        setAssets([]);
        setLoading(false);
        setError(null);
        setSearchSource(null);
        return;
      }

      // Set up debounced search
      debounceTimeoutRef.current = setTimeout(async () => {
        try {
          setLoading(true);
          setError(null);

          // Cancel any existing request
          if (currentRequestRef.current) {
            currentRequestRef.current.abort();
          }

          // Create new abort controller for this request
          const abortController = new AbortController();
          currentRequestRef.current = abortController;

          let combinedResults: CombinedSearchResult[] = [];
          let usedSource: "database" | "eodhd" | "combined" = "database";

          if (databaseOnly) {
            // Database-only search mode (for SELL transactions with portfolio filtering)
            try {
              const dbResults = await searchDatabase(
                term,
                abortController.signal
              );

              if (abortController.signal.aborted) return;

              combinedResults = convertDatabaseResults(dbResults);
              usedSource = "database";
            } catch (dbError) {
              console.error("Database search error:", dbError);
              setError(
                dbError instanceof Error
                  ? dbError.message
                  : "A apărut o eroare la căutarea în baza de date"
              );
            }
          } else if (prioritizeEODHD) {
            // Try EODHD first, fallback to database if needed
            try {
              const eodhResults = await searchEODHD(
                term,
                abortController.signal
              );

              if (abortController.signal.aborted) return;

              if (eodhResults.length > 0) {
                // Cache EODHD results in background (non-blocking)
                cacheSearchResultsInBackground(eodhResults);

                // Get existing tickers to mark which ones already exist in our database
                const dbResults = await searchDatabase(
                  term,
                  abortController.signal
                );

                if (abortController.signal.aborted) return;

                const existingTickers = new Set(
                  dbResults.map((asset) => asset.ticker.toUpperCase())
                );

                // Convert both EODHD and database results
                const eodhCombinedResults = convertEODHDResults(
                  eodhResults,
                  existingTickers
                );
                const dbCombinedResults = convertDatabaseResults(dbResults);

                // Combine results: database results first (existing assets), then EODHD results
                // Remove duplicates by checking if the same ticker already exists
                const allTickers = new Set<string>();
                combinedResults = [];

                // Add database results first (they are existing assets, so prioritize them)
                for (const dbResult of dbCombinedResults) {
                  const tickerKey = dbResult.ticker.toUpperCase();
                  if (!allTickers.has(tickerKey)) {
                    allTickers.add(tickerKey);
                    combinedResults.push(dbResult);
                  }
                }

                // Add EODHD results, avoiding duplicates
                for (const eodhResult of eodhCombinedResults) {
                  // For EODHD results, use displayTicker (with exchange) as the unique key
                  // This ensures exchange variants like VWCE.XETRA, VWCE.F, VWCE.BE are all shown
                  const uniqueKey =
                    eodhResult.displayTicker?.toUpperCase() ||
                    eodhResult.ticker.toUpperCase();

                  if (!allTickers.has(uniqueKey)) {
                    allTickers.add(uniqueKey);
                    combinedResults.push(eodhResult);
                  }
                }

                usedSource = "combined";
              } else {
                // Fallback to database search
                const dbResults = await searchDatabase(
                  term,
                  abortController.signal
                );

                if (abortController.signal.aborted) return;

                combinedResults = convertDatabaseResults(dbResults);
                usedSource = "database";
              }
            } catch (eodhError) {
              console.warn(
                "EODHD search failed, falling back to database:",
                eodhError
              );

              // Fallback to database search
              try {
                const dbResults = await searchDatabase(
                  term,
                  abortController.signal
                );

                if (abortController.signal.aborted) return;

                combinedResults = convertDatabaseResults(dbResults);
                usedSource = "database";
              } catch (dbError) {
                throw dbError; // Re-throw database error if both fail
              }
            }
          } else {
            // Database first approach
            const dbResults = await searchDatabase(
              term,
              abortController.signal
            );

            if (abortController.signal.aborted) return;

            combinedResults = convertDatabaseResults(dbResults);
            usedSource = "database";
          }

          // Only update state if this is still the current request
          if (currentRequestRef.current === abortController) {
            setAssets(combinedResults);
            setSearchSource(usedSource);
            setError(null);
          }
        } catch (err) {
          // Only update error state if this is still the current request and not aborted
          if (
            currentRequestRef.current &&
            !currentRequestRef.current.signal.aborted
          ) {
            console.error("Search error:", err);
            setError(
              err instanceof Error
                ? err.message
                : "A apărut o eroare la căutarea simbolurilor"
            );
            setAssets([]);
            setSearchSource(null);
          }
        } finally {
          // Only update loading state if this is still the current request
          if (
            currentRequestRef.current &&
            !currentRequestRef.current.signal.aborted
          ) {
            setLoading(false);
          }
        }
      }, debounceMs);
    },
    [
      debounceMs,
      minSearchLength,
      prioritizeEODHD,
      databaseOnly,
      searchEODHD,
      searchDatabase,
      convertEODHDResults,
      convertDatabaseResults,
      cacheSearchResultsInBackground,
    ]
  );

  // Effect to trigger search when search term changes
  useEffect(() => {
    debouncedSearch(searchTerm);

    // Cleanup function
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (currentRequestRef.current) {
        currentRequestRef.current.abort();
      }
    };
  }, [searchTerm, debouncedSearch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (currentRequestRef.current) {
        currentRequestRef.current.abort();
      }
    };
  }, []);

  // Clear search function
  const clearSearch = useCallback(() => {
    setSearchTerm("");
    setAssets([]);
    setError(null);
    setLoading(false);
    setSearchSource(null);

    // Cancel any pending requests
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    if (currentRequestRef.current) {
      currentRequestRef.current.abort();
    }
  }, []);

  return {
    assets,
    loading,
    error,
    searchTerm,
    setSearchTerm,
    clearSearch,
    searchSource,
  };
}
