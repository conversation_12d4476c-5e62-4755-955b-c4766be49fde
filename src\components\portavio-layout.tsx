"use client";

import { Footer } from "@/components/footer";
import { Header } from "@/components/header";
import { usePathname } from "next/navigation";
import { Suspense } from "react";

interface PortavioLayoutProps {
  children: React.ReactNode;
}

export function PortavioLayout({ children }: PortavioLayoutProps) {
  const pathname = usePathname();
  const isLandingPage = pathname === "/";

  if (isLandingPage) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen flex flex-col bg-background theme-transition">
      <Header variant="page" />
      <main className="flex-1 pt-20">
        <Suspense>{children}</Suspense>
      </main>
      <Footer />
    </div>
  );
}
