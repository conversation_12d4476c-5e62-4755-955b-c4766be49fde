import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { hasuraQuery } from "@/utils/db/hasura";

// Validation schema for query parameters
const checkExistsParamsSchema = z.object({
  ticker: z
    .string()
    .min(1, "Simbolul este obligatoriu")
    .max(20, "Simbolul nu poate avea mai mult de 20 de caractere"),
});

// Response types
interface CheckExistsSuccessResponse {
  success: true;
  exists: boolean;
  ticker: string;
  asset?: {
    asset_id: number;
    ticker: string;
    name: string;
    company: string;
    currency?: {
      code: string;
    };
  };
}

interface CheckExistsErrorResponse {
  success: false;
  error: string;
  code?: string;
}

// GraphQL query to get asset with currency information
const GET_ASSET_WITH_CURRENCY = `
  query GetAssetWithCurrency($ticker: String!) {
    ptvuser_asset(
      where: { ticker: { _eq: $ticker } }
      limit: 1
    ) {
      asset_id
      ticker
      name
      company
      currency {
        code
      }
    }
  }
`;

// GET /api/assets/check-exists - Check if a ticker exists in the database
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const ticker = searchParams.get("ticker");

    // Validate parameters
    let validatedParams;
    try {
      validatedParams = checkExistsParamsSchema.parse({ ticker });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: "Parametri invalizi",
            code: "VALIDATION_ERROR",
          } as CheckExistsErrorResponse,
          { status: 400 }
        );
      }
      throw error;
    }

    console.log(
      `Checking asset existence for ticker: ${validatedParams.ticker}`
    );

    // Get asset with currency information
    const result = await hasuraQuery<{
      ptvuser_asset: Array<{
        asset_id: number;
        ticker: string;
        name: string;
        company: string;
        currency?: {
          code: string;
        };
      }>;
    }>(GET_ASSET_WITH_CURRENCY, {
      variables: { ticker: validatedParams.ticker.toUpperCase() },
    });

    const asset = result.ptvuser_asset?.[0];
    const exists = asset !== undefined;

    const successResponse: CheckExistsSuccessResponse = {
      success: true,
      exists,
      ticker: validatedParams.ticker.toUpperCase(),
      asset: asset
        ? {
            asset_id: asset.asset_id,
            ticker: asset.ticker,
            name: asset.name,
            company: asset.company,
            currency: asset.currency,
          }
        : undefined,
    };

    return NextResponse.json(successResponse);
  } catch (error) {
    console.error("Error in asset existence check API:", error);

    return NextResponse.json(
      {
        success: false,
        error: "A apărut o eroare la verificarea existenței activului",
        code: "INTERNAL_ERROR",
      } as CheckExistsErrorResponse,
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește GET pentru verificarea existenței",
      code: "METHOD_NOT_ALLOWED",
    } as CheckExistsErrorResponse,
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește GET pentru verificarea existenței",
      code: "METHOD_NOT_ALLOWED",
    } as CheckExistsErrorResponse,
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: "Metodă nepermisă. Folosește GET pentru verificarea existenței",
      code: "METHOD_NOT_ALLOWED",
    } as CheckExistsErrorResponse,
    { status: 405 }
  );
}
