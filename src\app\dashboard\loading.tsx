import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

export default function DashboardLoading() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <LoadingSpinner size="sm" variant="portavio" />
          <Skeleton className="h-8 w-32" />
        </div>
        <Skeleton className="h-4 w-64" />
      </div>

      {/* Portfolio Filter */}
      <div className="mb-8">
        <Skeleton className="h-10 w-full max-w-md" />
      </div>

      {/* Dashboard Grid Layout */}
      <div className="grid gap-6">
        {/* Row 1: 2 cards (left wider than right) */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Card 1 - Performance Overview (2/3 width) */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <Skeleton className="h-6 w-48" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-8 w-24" />
                  <Skeleton className="h-5 w-16" />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center">
                <div className="text-center space-y-3">
                  <LoadingSpinner size="lg" variant="portavio" />
                  <Skeleton className="h-4 w-48 mx-auto" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Card 2 - Portfolio Composition (1/3 width) */}
          <Card className="lg:col-span-1">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-5 w-16" />
              </div>
              
              {/* View Selector Tabs */}
              <div className="flex flex-wrap gap-1 mt-4">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Skeleton key={i} className="h-7 w-16" />
                ))}
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                {/* Donut Chart Placeholder */}
                <div className="flex justify-center">
                  <div className="h-48 w-48 rounded-full border-8 border-muted animate-pulse" />
                </div>

                {/* Legend Placeholder */}
                <div className="mt-8">
                  <div className="grid grid-cols-1 gap-2 max-h-32">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Skeleton className="w-3 h-3 rounded-full" />
                          <Skeleton className="h-4 w-24" />
                        </div>
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-4 w-12" />
                          <Skeleton className="h-4 w-16" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Row 2: 2 cards (left wider than right) */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Card 3 - Dividends (2/3 width) */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <Skeleton className="h-6 w-24" />
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <Skeleton className="h-4 w-20 mb-1" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                  <div className="text-center">
                    <Skeleton className="h-4 w-20 mb-1" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center">
                <div className="text-center space-y-3">
                  <LoadingSpinner size="lg" variant="portavio" />
                  <Skeleton className="h-4 w-40 mx-auto" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Card 4 - Key Metrics (1/3 width) */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <Skeleton className="h-6 w-28" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex justify-between items-center">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-5 w-16" />
                  </div>
                ))}
                <div className="mt-6 pt-4 border-t">
                  <Skeleton className="h-3 w-32 mx-auto" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Row 3: 1 full-width card */}
        <div className="grid grid-cols-1 gap-6">
          {/* Card 5 - Holdings Table */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-24" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Table header */}
                <div className="grid grid-cols-6 gap-4 border-b pb-2">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <Skeleton key={i} className="h-4 w-16" />
                  ))}
                </div>
                
                {/* Table rows */}
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="grid grid-cols-6 gap-4 py-2">
                    {Array.from({ length: 6 }).map((_, j) => (
                      <Skeleton key={j} className="h-4 w-full" />
                    ))}
                  </div>
                ))}
                
                <div className="mt-6 pt-4 border-t text-center">
                  <Skeleton className="h-3 w-48 mx-auto" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
