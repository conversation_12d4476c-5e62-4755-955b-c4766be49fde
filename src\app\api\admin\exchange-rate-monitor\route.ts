import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { 
  getExchangeRateMonitoringMetrics, 
  logExchangeRateStatus,
  resetExchangeRateMonitoring,
  fullResetExchangeRateSystem 
} from "@/utils/exchange-rate-monitor";

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Nu sunteți autentificat" },
        { status: 401 }
      );
    }

    // Get monitoring metrics
    const metrics = getExchangeRateMonitoringMetrics();
    
    // Log current status to console for debugging
    logExchangeRateStatus();

    return NextResponse.json({
      success: true,
      data: metrics,
      message: "Metrici de monitorizare obținute cu succes",
    });
  } catch (error) {
    console.error("Error in exchange rate monitor API:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Eroare la obținerea metricilor de monitorizare",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Nu sunteți autentificat" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case "reset-metrics":
        resetExchangeRateMonitoring();
        return NextResponse.json({
          success: true,
          message: "Metricile de monitorizare au fost resetate",
        });

      case "full-reset":
        fullResetExchangeRateSystem();
        return NextResponse.json({
          success: true,
          message: "Sistemul de cursuri de schimb a fost resetat complet",
        });

      case "log-status":
        logExchangeRateStatus();
        return NextResponse.json({
          success: true,
          message: "Statusul a fost înregistrat în consolă",
        });

      default:
        return NextResponse.json(
          { error: "Acțiune necunoscută" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error in exchange rate monitor API:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Eroare la executarea acțiunii de monitorizare",
      },
      { status: 500 }
    );
  }
}
