/**
 * Test suite for currency conversion functions
 * Run with: node src/tests/test-currency-conversion.js
 */

// Mock currency conversion function
function convertAmount(amount, fromCurrency, toCurrency, exchangeRates) {
  // If same currency, no conversion needed
  if (fromCurrency === toCurrency) {
    return amount;
  }

  // Look for direct conversion rate with .FOREX suffix
  const directRate = exchangeRates.get(`${fromCurrency}${toCurrency}.FOREX`);
  if (directRate !== undefined) {
    return amount * directRate;
  }

  // Look for inverse conversion rate with .FOREX suffix
  const inverseRate = exchangeRates.get(`${toCurrency}${fromCurrency}.FOREX`);
  if (inverseRate !== undefined && inverseRate !== 0) {
    return amount / inverseRate;
  }

  // If no rate found, return original amount (1:1 fallback)
  console.warn(
    `No exchange rate found for ${fromCurrency} to ${toCurrency}, using 1:1`
  );
  return amount;
}

// Mock function to extract portfolio currencies
function extractPortfolioCurrencies(assetData) {
  const currencies = new Set();

  assetData.forEach((asset) => {
    const currency = asset.currency?.code || "EUR";
    currencies.add(currency);
  });

  return Array.from(currencies);
}

// Mock function to get required exchange rates
function getRequiredExchangeRates(portfolioCurrencies, displayCurrency) {
  const requiredRates = [];

  portfolioCurrencies.forEach((currency) => {
    if (currency !== displayCurrency) {
      requiredRates.push(`${currency}${displayCurrency}.FOREX`);
      requiredRates.push(`${displayCurrency}${currency}.FOREX`);
    }
  });

  return requiredRates;
}

// Test data with .FOREX suffix
const mockExchangeRates = new Map([
  ["USDEUR.FOREX", 0.85],
  ["EURUSD.FOREX", 1.18],
  ["RONEUR.FOREX", 0.2], // Fixed typo: RONUER -> RONEUR
  ["EURRON.FOREX", 5.0],
  ["USDRON.FOREX", 4.25],
  ["RONUSD.FOREX", 0.235],
  ["GBPEUR.FOREX", 1.15],
  ["EURGBP.FOREX", 0.87],
]);

const mockAssetData = new Map([
  [
    "AAPL",
    {
      name: "Apple Inc.",
      currency: { code: "USD" },
    },
  ],
  [
    "TLV.RO",
    {
      name: "Banca Transilvania",
      currency: { code: "RON" },
    },
  ],
  [
    "ASML",
    {
      name: "ASML Holding",
      currency: { code: "EUR" },
    },
  ],
  [
    "LLOY.L",
    {
      name: "Lloyds Banking Group",
      currency: { code: "GBP" },
    },
  ],
]);

// Test functions
function assertEqual(actual, expected, testName, tolerance = 0.01) {
  if (Math.abs(actual - expected) < tolerance) {
    console.log(`✅ PASS: ${testName}`);
    console.log(`   Expected: ${expected}, Actual: ${actual}`);
    return true;
  } else {
    console.log(`❌ FAIL: ${testName}`);
    console.log(`   Expected: ${expected}, Actual: ${actual}`);
    return false;
  }
}

function assertArrayEqual(actual, expected, testName) {
  const actualSorted = [...actual].sort();
  const expectedSorted = [...expected].sort();

  if (
    actualSorted.length === expectedSorted.length &&
    actualSorted.every((val, index) => val === expectedSorted[index])
  ) {
    console.log(`✅ PASS: ${testName}`);
    console.log(`   Arrays match: [${actualSorted.join(", ")}]`);
    return true;
  } else {
    console.log(`❌ FAIL: ${testName}`);
    console.log(`   Expected: [${expectedSorted.join(", ")}]`);
    console.log(`   Actual: [${actualSorted.join(", ")}]`);
    return false;
  }
}

// Run tests
console.log("🧪 Testing currency conversion functions\n");

// Test 1: Same currency conversion
console.log("Test 1: Same currency conversion");
const sameCurrency = convertAmount(100, "EUR", "EUR", mockExchangeRates);
assertEqual(sameCurrency, 100, "Same currency (EUR to EUR)");

// Test 2: Direct conversion rate
console.log("\nTest 2: Direct conversion rate");
const usdToEur = convertAmount(100, "USD", "EUR", mockExchangeRates);
assertEqual(usdToEur, 85, "USD to EUR direct conversion"); // 100 * 0.85

const ronToEur = convertAmount(500, "RON", "EUR", mockExchangeRates);
assertEqual(ronToEur, 100, "RON to EUR direct conversion"); // 500 * 0.20

// Test 3: Inverse conversion rate
console.log("\nTest 3: Inverse conversion rate");
const eurToUsd = convertAmount(85, "EUR", "USD", mockExchangeRates);
assertEqual(eurToUsd, 100.3, "EUR to USD inverse conversion", 0.1); // 85 * 1.18

// Test 4: Missing exchange rate (fallback to 1:1)
console.log("\nTest 4: Missing exchange rate fallback");
const missingRate = convertAmount(100, "JPY", "EUR", mockExchangeRates);
assertEqual(missingRate, 100, "Missing rate fallback (JPY to EUR)");

// Test 5: Zero amount conversion
console.log("\nTest 5: Zero amount conversion");
const zeroAmount = convertAmount(0, "USD", "EUR", mockExchangeRates);
assertEqual(zeroAmount, 0, "Zero amount conversion");

// Test 6: Negative amount conversion
console.log("\nTest 6: Negative amount conversion");
const negativeAmount = convertAmount(-50, "USD", "EUR", mockExchangeRates);
assertEqual(negativeAmount, -42.5, "Negative amount conversion"); // -50 * 0.85

// Test 7: Large amount conversion
console.log("\nTest 7: Large amount conversion");
const largeAmount = convertAmount(1000000, "USD", "EUR", mockExchangeRates);
assertEqual(largeAmount, 850000, "Large amount conversion"); // 1000000 * 0.85

// Test 8: Extract portfolio currencies
console.log("\nTest 8: Extract portfolio currencies");
const portfolioCurrencies = extractPortfolioCurrencies(mockAssetData);
const expectedCurrencies = ["USD", "RON", "EUR", "GBP"];
assertArrayEqual(
  portfolioCurrencies,
  expectedCurrencies,
  "Portfolio currencies extraction"
);

// Test 9: Get required exchange rates
console.log("\nTest 9: Get required exchange rates");
const requiredRates = getRequiredExchangeRates(["USD", "RON"], "EUR");
const expectedRates = [
  "USDEUR.FOREX",
  "EURUSD.FOREX",
  "RONEUR.FOREX",
  "EURRON.FOREX",
];
assertArrayEqual(requiredRates, expectedRates, "Required exchange rates");

// Test 10: Get required rates with display currency in portfolio
console.log("\nTest 10: Required rates with display currency in portfolio");
const requiredRatesWithEur = getRequiredExchangeRates(
  ["USD", "EUR", "RON"],
  "EUR"
);
const expectedRatesWithEur = [
  "USDEUR.FOREX",
  "EURUSD.FOREX",
  "RONEUR.FOREX",
  "EURRON.FOREX",
];
assertArrayEqual(
  requiredRatesWithEur,
  expectedRatesWithEur,
  "Required rates with EUR in portfolio"
);

// Test 11: Empty portfolio currencies
console.log("\nTest 11: Empty portfolio currencies");
const emptyRequiredRates = getRequiredExchangeRates([], "EUR");
assertArrayEqual(emptyRequiredRates, [], "Empty portfolio required rates");

// Test 12: Chain conversion accuracy
console.log("\nTest 12: Chain conversion accuracy");
// Convert USD -> EUR -> RON and compare with direct USD -> RON
const usdToEurStep = convertAmount(100, "USD", "EUR", mockExchangeRates); // 85 EUR
const eurToRonStep = convertAmount(
  usdToEurStep,
  "EUR",
  "RON",
  mockExchangeRates
); // 85 * 5 = 425 RON
const directUsdToRon = convertAmount(100, "USD", "RON", mockExchangeRates); // 100 * 4.25 = 425 RON

assertEqual(eurToRonStep, directUsdToRon, "Chain vs direct conversion", 0.1);

// Test 13: Precision with small amounts
console.log("\nTest 13: Precision with small amounts");
const smallAmount = convertAmount(0.01, "USD", "EUR", mockExchangeRates);
assertEqual(smallAmount, 0.0085, "Small amount precision"); // 0.01 * 0.85

// Test 14: Multiple conversions consistency
console.log("\nTest 14: Multiple conversions consistency");
const amount1 = convertAmount(100, "USD", "EUR", mockExchangeRates);
const amount2 = convertAmount(100, "USD", "EUR", mockExchangeRates);
assertEqual(amount1, amount2, "Multiple conversions consistency");

// Test 15: Round-trip conversion
console.log("\nTest 15: Round-trip conversion");
const original = 100;
const converted = convertAmount(original, "USD", "EUR", mockExchangeRates);
const roundTrip = convertAmount(converted, "EUR", "USD", mockExchangeRates);
assertEqual(roundTrip, original, "Round-trip conversion", 0.5); // Allow larger tolerance for rounding errors

console.log("\n🏁 Test suite completed!");
