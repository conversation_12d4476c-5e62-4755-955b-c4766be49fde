import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { getUserPortfoliosWithMetrics } from "@/utils/db/portfolio-queries";

// GET /api/portfolios/metrics - Get user's portfolios with calculated metrics
export async function GET() {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const portfolios = await getUserPortfoliosWithMetrics(session.user.id);

    return NextResponse.json({
      portfolios,
      count: portfolios.length,
      message: "Portofoliile au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error fetching portfolios with metrics:", error);
    return NextResponse.json(
      { error: "Nu s-au putut încărca portofoliile" },
      { status: 500 }
    );
  }
}
