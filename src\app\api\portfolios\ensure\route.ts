import { auth } from "@/lib/auth";
import { ensureUserHasPortfolio } from "@/utils/db/portfolio-queries";
import { headers } from "next/headers";
import { NextResponse } from "next/server";

// POST /api/portfolios/ensure - Ensure user has at least one portfolio
export async function POST() {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const result = await ensureUserHasPortfolio(session.user.id);

    return NextResponse.json({
      portfolios: result.portfolios,
      defaultCreated: result.defaultCreated,
      message: result.defaultCreated
        ? "A fost creat un portofoliu implicit"
        : "Portofoliile existente au fost încărcate",
    });
  } catch (error) {
    console.error("Error ensuring user has portfolio:", error);
    return NextResponse.json(
      { error: "Nu s-a putut verifica portofoliul utilizatorului" },
      { status: 500 }
    );
  }
}
