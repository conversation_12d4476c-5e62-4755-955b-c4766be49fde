/**
 * EODHD Search API Client
 * Handles search requests to EODHD API for ticker search functionality
 */

import { hasuraQuery } from "@/utils/db/hasura";

// GraphQL query to get EODHD API key from database
const GET_EODHD_API_KEY = `
  query GetEODHDApiKey {
    ptvuser_data_provider(where: {name: {_eq: "eodhd"}}) {
      api_key
      name
    }
  }
`;

// Database response type
interface DataProviderResponse {
  ptvuser_data_provider: Array<{
    api_key: string;
    name: string;
  }>;
}

// EODHD Search API Response Types
export interface EODHDSearchResult {
  Code: string;
  Exchange: string;
  Name: string;
  Type: string;
  Country: string;
  Currency: string;
  ISIN: string | null;
  previousClose: number;
  previousCloseDate: string;
}

export type EODHDSearchResponse = EODHDSearchResult[];

// Processed search result for internal use
export interface ProcessedSearchResult {
  ticker: string;
  name: string;
  exchange: string;
  type: string;
  country: string;
  currency: string;
  isin?: string;
  previousClose?: number;
  previousCloseDate?: string;
  // Computed display fields
  displayTicker: string; // Code.Exchange format for display
  description: string; // Name • Exchange • Country format
}

// Error types
export class EODHDSearchError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = "EODHDSearchError";
  }
}

// Client configuration
interface EODHDSearchClientConfig {
  apiToken: string;
  baseURL?: string;
  timeout?: number;
}

/**
 * EODHD Search API Client
 */
export class EODHDSearchClient {
  private config: EODHDSearchClientConfig;

  constructor(config: EODHDSearchClientConfig) {
    this.config = {
      baseURL: "https://eodhd.com/api",
      timeout: 10000, // 10 seconds default
      ...config,
    };
  }

  /**
   * Search for tickers using EODHD Search API
   */
  async searchTickers(query: string): Promise<ProcessedSearchResult[]> {
    if (!query || query.trim().length === 0) {
      return [];
    }

    const trimmedQuery = query.trim();

    // Build search URL
    const searchUrl = `${this.config.baseURL}/search/${encodeURIComponent(
      trimmedQuery
    )}`;
    const params = new URLSearchParams({
      api_token: this.config.apiToken,
      fmt: "json",
    });

    const fullUrl = `${searchUrl}?${params.toString()}`;

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, this.config.timeout);

      const response = await fetch(fullUrl, {
        method: "GET",
        headers: {
          Accept: "application/json",
          "User-Agent": "Portavio/1.0",
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        // Try to get error details from response
        try {
          const errorData = await response.text();
          if (errorData) {
            errorMessage += ` - ${errorData}`;
          }
        } catch {
          // Ignore error parsing error response
        }

        throw new EODHDSearchError(
          `Căutarea a eșuat: ${errorMessage}`,
          response.status
        );
      }

      const data: EODHDSearchResponse = await response.json();

      // Process and return results
      return this.processSearchResults(data);
    } catch (error) {
      if (error instanceof EODHDSearchError) {
        throw error;
      }

      if (error instanceof Error && error.name === "AbortError") {
        throw new EODHDSearchError("Căutarea a expirat. Încearcă din nou.");
      }

      console.error("EODHD Search API error:", error);
      throw new EODHDSearchError(
        "A apărut o eroare la căutarea simbolurilor",
        undefined,
        error
      );
    }
  }

  /**
   * Process raw EODHD search results into internal format
   */
  private processSearchResults(
    results: EODHDSearchResponse
  ): ProcessedSearchResult[] {
    if (!Array.isArray(results)) {
      return [];
    }

    return results.map((result) => {
      // Create display ticker (Code.Exchange format)
      const displayTicker = result.Exchange
        ? `${result.Code}.${result.Exchange}`
        : result.Code;

      // Create description (Name • Exchange • Country format)
      const descriptionParts = [result.Name];
      if (result.Exchange && result.Exchange !== "Unknown") {
        descriptionParts.push(result.Exchange);
      }
      if (result.Country && result.Country !== "Unknown") {
        descriptionParts.push(result.Country);
      }
      const description = descriptionParts.join(" • ");

      return {
        ticker: result.Code,
        name: result.Name,
        exchange: result.Exchange,
        type: result.Type,
        country: result.Country,
        currency: result.Currency,
        isin: result.ISIN || undefined,
        previousClose: result.previousClose,
        previousCloseDate: result.previousCloseDate,
        displayTicker,
        description,
      };
    });
  }
}

/**
 * Create a configured instance of the EODHD Search Client
 * Retrieves API key from database instead of environment variables
 */
export async function createEODHDSearchClient(): Promise<EODHDSearchClient> {
  try {
    // Query database for EODHD API key
    const result = await hasuraQuery<DataProviderResponse>(GET_EODHD_API_KEY);

    // Check if EODHD provider exists in database
    if (
      !result.ptvuser_data_provider ||
      result.ptvuser_data_provider.length === 0
    ) {
      throw new Error("EODHD data provider not found in database");
    }

    const eodhProvider = result.ptvuser_data_provider[0];

    // Check if API key is available
    if (!eodhProvider.api_key || eodhProvider.api_key.trim() === "") {
      throw new Error("EODHD API key not configured in database");
    }

    return new EODHDSearchClient({
      apiToken: eodhProvider.api_key,
      timeout: 10000,
    });
  } catch (error) {
    console.error("Error creating EODHD search client:", error);

    if (error instanceof Error) {
      throw new Error(`Failed to initialize EODHD client: ${error.message}`);
    }

    throw new Error("Failed to initialize EODHD client: Unknown error");
  }
}
