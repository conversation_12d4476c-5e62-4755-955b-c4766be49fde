import { cacheSearchResults } from "@/utils/db/search-results-queries";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

// Validation schema for caching search results
const cacheSearchResultsSchema = z.object({
  results: z
    .array(
      z.object({
        code: z.string().min(1).max(50),
        exchange: z.string().min(1).max(10),
        name: z.string().min(1).max(500),
        type: z.string().min(1).max(100),
        country: z.string().min(1).max(100),
        currency: z.string().min(1).max(10),
        isin: z.string().max(20).optional().nullable(),
      })
    )
    .max(25, "Nu se pot cache mai mult de 25 de rezultate odată"),
});

// Response types
interface CacheSuccessResponse {
  success: true;
  data: {
    cached: number;
    totalCached: number;
  };
  message: string;
}

interface CacheErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

// POST /api/search/cache - Cache search results in background
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    let validatedData;
    try {
      validatedData = cacheSearchResultsSchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: "Date invalide pentru cache",
            code: "VALIDATION_ERROR",
            details: error.errors.map((err) => ({
              field: err.path.join("."),
              message: err.message,
            })),
          } as CacheErrorResponse,
          { status: 400 }
        );
      }
      throw error;
    }

    console.log(`Caching ${validatedData.results.length} search results`);

    // Cache the search results using UPSERT (insert new or update timestamp for existing)
    const cachedResults = await cacheSearchResults(validatedData.results);
    const totalCached = cachedResults.length;

    console.log(
      `Search results caching completed: ${totalCached} results processed`
    );

    const successResponse: CacheSuccessResponse = {
      success: true,
      data: {
        cached: totalCached,
        totalCached: totalCached,
      },
      message: `${totalCached} rezultate au fost salvate în cache cu succes`,
    };

    return NextResponse.json(successResponse);
  } catch (error) {
    console.error("Error in search cache API:", error);

    // Handle specific database errors
    if (error instanceof Error) {
      if (error.message.includes("constraint")) {
        return NextResponse.json(
          {
            success: false,
            error: "Eroare de integritate a datelor în cache",
            code: "CONSTRAINT_ERROR",
            details: error.message,
          } as CacheErrorResponse,
          { status: 400 }
        );
      }

      if (error.message.includes("connection")) {
        return NextResponse.json(
          {
            success: false,
            error: "Eroare de conexiune la baza de date",
            code: "CONNECTION_ERROR",
            details: error.message,
          } as CacheErrorResponse,
          { status: 503 }
        );
      }
    }

    // Handle generic errors
    return NextResponse.json(
      {
        success: false,
        error: "A apărut o eroare neașteptată la salvarea în cache",
        code: "INTERNAL_ERROR",
        details: error instanceof Error ? error.message : String(error),
      } as CacheErrorResponse,
      { status: 500 }
    );
  }
}

// GET /api/search/cache - Get cache statistics (for debugging)
export async function GET() {
  try {
    // Return cache statistics
    const { getSearchResultsCount } = await import(
      "@/utils/db/search-results-queries"
    );
    const count = await getSearchResultsCount();

    return NextResponse.json({
      success: true,
      data: {
        totalCached: count,
      },
      message: `Cache-ul conține ${count} rezultate`,
    });
  } catch (error) {
    console.error("Error in search cache GET API:", error);

    return NextResponse.json(
      {
        success: false,
        error: "A apărut o eroare la obținerea statisticilor cache",
        code: "INTERNAL_ERROR",
        details: error instanceof Error ? error.message : String(error),
      } as CacheErrorResponse,
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error:
        "Metodă nepermisă. Folosește POST pentru cache sau GET pentru statistici",
      code: "METHOD_NOT_ALLOWED",
    } as CacheErrorResponse,
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error:
        "Metodă nepermisă. Folosește POST pentru cache sau GET pentru statistici",
      code: "METHOD_NOT_ALLOWED",
    } as CacheErrorResponse,
    { status: 405 }
  );
}
