"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Play, RotateCcw, Activity } from "lucide-react";
import { runAllExchangeRateTests } from "@/utils/test-exchange-rate-deduplication";

interface TestResults {
  deduplication: {
    success: boolean;
    totalRequested?: number;
    actualApiCalls?: number;
    apiCallsSaved?: number;
    efficiencyPercentage?: number;
    duration?: number;
    error?: string;
  };
  performance: {
    sequentialDuration: number;
    parallelDuration: number;
    improvement: number;
  };
}

export function ExchangeRateTestPanel() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResults | null>(null);
  const [monitoringData, setMonitoringData] = useState<any>(null);

  const runTests = async () => {
    setIsRunning(true);
    setResults(null);
    
    try {
      const testResults = await runAllExchangeRateTests();
      setResults(testResults);
    } catch (error) {
      console.error("Test failed:", error);
      setResults({
        deduplication: {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        },
        performance: {
          sequentialDuration: 0,
          parallelDuration: 0,
          improvement: 0,
        },
      });
    } finally {
      setIsRunning(false);
    }
  };

  const fetchMonitoringData = async () => {
    try {
      const response = await fetch("/api/admin/exchange-rate-monitor");
      if (response.ok) {
        const data = await response.json();
        setMonitoringData(data.data);
      }
    } catch (error) {
      console.error("Failed to fetch monitoring data:", error);
    }
  };

  const resetSystem = async () => {
    try {
      await fetch("/api/admin/exchange-rate-monitor", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "full-reset" }),
      });
      setMonitoringData(null);
      setResults(null);
    } catch (error) {
      console.error("Failed to reset system:", error);
    }
  };

  const getEfficiencyBadgeColor = (percentage?: number) => {
    if (!percentage) return "secondary";
    if (percentage > 70) return "default"; // Green
    if (percentage > 50) return "secondary"; // Yellow
    return "destructive"; // Red
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Exchange Rate Deduplication Test Panel
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button 
              onClick={runTests} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              {isRunning ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              Run Deduplication Tests
            </Button>
            
            <Button 
              variant="outline" 
              onClick={fetchMonitoringData}
              className="flex items-center gap-2"
            >
              <Activity className="h-4 w-4" />
              Get Monitoring Data
            </Button>
            
            <Button 
              variant="destructive" 
              onClick={resetSystem}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset System
            </Button>
          </div>

          {isRunning && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Running tests... This simulates 5 dashboard components requesting exchange rates simultaneously.
            </div>
          )}
        </CardContent>
      </Card>

      {results && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Deduplication Results */}
              <div className="space-y-2">
                <h3 className="font-semibold">Deduplication Test</h3>
                {results.deduplication.success ? (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Total Requests:</span>
                      <span className="font-mono">{results.deduplication.totalRequested}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Actual API Calls:</span>
                      <span className="font-mono">{results.deduplication.actualApiCalls}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>API Calls Saved:</span>
                      <span className="font-mono text-green-600">{results.deduplication.apiCallsSaved}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Efficiency:</span>
                      <Badge variant={getEfficiencyBadgeColor(results.deduplication.efficiencyPercentage)}>
                        {results.deduplication.efficiencyPercentage?.toFixed(2)}%
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Duration:</span>
                      <span className="font-mono">{results.deduplication.duration}ms</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-red-600">
                    Error: {results.deduplication.error}
                  </div>
                )}
              </div>

              {/* Performance Results */}
              <div className="space-y-2">
                <h3 className="font-semibold">Performance Comparison</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Sequential:</span>
                    <span className="font-mono">{results.performance.sequentialDuration}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Parallel:</span>
                    <span className="font-mono">{results.performance.parallelDuration}ms</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Improvement:</span>
                    <Badge variant={results.performance.improvement > 0 ? "default" : "secondary"}>
                      {results.performance.improvement.toFixed(2)}%
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {monitoringData && (
        <Card>
          <CardHeader>
            <CardTitle>Live Monitoring Data</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{monitoringData.totalRequests}</div>
                <div className="text-sm text-muted-foreground">Total Requests</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{monitoringData.apiCallsSaved}</div>
                <div className="text-sm text-muted-foreground">API Calls Saved</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{monitoringData.currentCacheStats.size}</div>
                <div className="text-sm text-muted-foreground">Cache Size</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{monitoringData.efficiencyPercentage.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Efficiency</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
