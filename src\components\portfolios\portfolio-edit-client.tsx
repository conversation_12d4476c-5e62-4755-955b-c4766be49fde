"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { PortfolioEditForm } from "@/components/portfolios/portfolio-edit-form";
import { PortfolioEditFormData } from "@/lib/portfolio-schemas";
import { Portfolio } from "@/utils/db/portfolio-queries";
import { portfoliosKeys } from "@/hooks/use-portfolios-query";
import { useDashboardInvalidation } from "@/hooks/use-dashboard-invalidation";
import { useQueryClient } from "@tanstack/react-query";
import { ArrowLeft, AlertCircle, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

interface PortfolioEditClientProps {
  portfolio: Portfolio;
}

export function PortfolioEditClient({ portfolio }: PortfolioEditClientProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { invalidateAllDashboardQueries } = useDashboardInvalidation();

  const handleSubmit = async (data: PortfolioEditFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/portfolios/${portfolio.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Nu s-a putut actualiza portofoliul"
        );
      }

      await response.json();

      toast.success("Portofoliul a fost actualizat cu succes!");

      queryClient.invalidateQueries({
        queryKey: portfoliosKeys.all,
      });

      // Invalidate dashboard queries since portfolio changes might affect dashboard
      invalidateAllDashboardQueries();

      router.push("/portfolios");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleGoBack}
          className="gap-2"
          disabled={isLoading}
        >
          <ArrowLeft className="h-4 w-4" />
          Înapoi
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Editare Portofoliu</h1>
          <p className="text-muted-foreground">
            Modifică detaliile portofoliului &ldquo;{portfolio.name}&ldquo;
          </p>
        </div>
      </div>

      {/* Global Error */}
      {error && (
        <div className="rounded-md bg-red-50 p-4 text-sm text-red-600 dark:text-red-400 dark:bg-red-900/20 flex items-center gap-2">
          <AlertCircle className="h-4 w-4" />
          <div>
            <p className="font-medium">A apărut o eroare</p>
            <p>{error}</p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-portavio-orange" />
            <p className="text-muted-foreground">
              Se actualizează portofoliul...
            </p>
          </div>
        </div>
      )}

      {/* Edit Form */}
      <PortfolioEditForm
        portfolio={portfolio}
        onSubmit={handleSubmit}
        isLoading={isLoading}
      />

      {/* Portfolio Info */}
      <div className="bg-muted/50 rounded-lg p-4 text-sm text-muted-foreground w-full max-w-2xl mx-auto">
        <h3 className="font-medium text-lg mb-2">Informații Portofoliu</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {/* <div>
            <span className="font-medium">ID:</span> {portfolio.id}
          </div> */}
          <div>
            <span className="font-medium">Creat la:</span>{" "}
            {new Date(portfolio.created_at).toLocaleDateString("ro-RO", {
              year: "numeric",
              month: "long",
              day: "numeric",
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
          <div>
            <span className="font-medium">Ultima actualizare:</span>{" "}
            {new Date(portfolio.updated_at).toLocaleDateString("ro-RO", {
              year: "numeric",
              month: "long",
              day: "numeric",
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
          <div>
            <span className="font-medium">Status actual:</span>{" "}
            <span
              className={
                portfolio.is_active
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }
            >
              {portfolio.is_active ? "Activ" : "Inactiv"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
