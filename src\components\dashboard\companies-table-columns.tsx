"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Building2, TrendingDown, TrendingUp } from "lucide-react";
import Image from "next/image";

import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import { CompanyData } from "@/utils/db/dashboard-queries";

const formatCurrency = (value: number, currency: SupportedCurrency): string => {
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const formatNumber = (value: number, decimals: number = 2): string => {
  return value.toLocaleString("ro-RO", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(2)}%`;
};

const getReturnColor = (value: number): string => {
  if (value > 0) return "text-green-600 dark:text-green-400";
  if (value < 0) return "text-red-600 dark:text-red-400";
  return "text-gray-600 dark:text-gray-400";
};

export function createCompaniesTableColumns(
  displayCurrency: SupportedCurrency
): ColumnDef<CompanyData>[] {
  return [
    {
      accessorKey: "logo_url",
      header: () => <div className="w-8"></div>,
      cell: ({ row }) => {
        const company = row.original;
        return (
          <div className="flex items-center justify-center">
            <div className="p-1 rounded-md bg-muted">
              {company.logo_url ? (
                <Image
                  src={company.logo_url}
                  alt={company.ticker}
                  className="h-6 w-6"
                  width={24}
                  height={24}
                />
              ) : (
                <Building2 className="h-6 w-6 text-muted-foreground" />
              )}
            </div>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 60,
      minSize: 60,
      maxSize: 60,
    },
    {
      accessorKey: "company",
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title="Companie/Ticker"
          isFirstColumn={true}
        />
      ),
      cell: ({ row }) => {
        const company = row.original;
        return (
          <div className="flex flex-col gap-1 min-w-0 items-start">
            <div className="font-semibold text-left truncate">
              {company.company}
            </div>
            <div className="text-sm text-muted-foreground truncate">
              {company.ticker}
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
      size: 220,
      minSize: 200,
      maxSize: 300,
    },
    {
      accessorKey: "shares",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Acțiuni" />
        </div>
      ),
      cell: ({ row }) => {
        const shares = row.getValue("shares") as number;
        return (
          <div className="text-right font-medium truncate">
            {formatNumber(shares, 0)}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
      minSize: 100,
      maxSize: 120,
    },
    {
      accessorKey: "currentPrice",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Preț curent" />
        </div>
      ),
      cell: ({ row }) => {
        const currentPrice = row.getValue("currentPrice") as number;
        return (
          <div className="text-right font-medium truncate">
            {formatCurrency(currentPrice, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 120,
      minSize: 120,
      maxSize: 140,
    },
    {
      accessorKey: "avgCost",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="DCA (Preț mediu)" />
        </div>
      ),
      cell: ({ row }) => {
        const avgCost = row.getValue("avgCost") as number;
        return (
          <div className="text-right font-medium truncate">
            {formatCurrency(avgCost, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 150,
      minSize: 150,
      maxSize: 160,
    },
    {
      accessorKey: "dcaPercentage",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="% DCA" />
        </div>
      ),
      cell: ({ row }) => {
        const dcaPercentage = row.getValue("dcaPercentage") as number;
        return (
          <div
            className={`text-right font-medium flex items-center justify-end gap-1 ${getReturnColor(
              dcaPercentage
            )}`}
          >
            {dcaPercentage > 0 ? (
              <TrendingUp className="h-3 w-3 flex-shrink-0" />
            ) : dcaPercentage < 0 ? (
              <TrendingDown className="h-3 w-3 flex-shrink-0" />
            ) : null}
            <span className="truncate">
              {dcaPercentage !== undefined
                ? formatPercentage(dcaPercentage)
                : "N/A"}
            </span>
          </div>
        );
      },
      enableSorting: true,
      size: 110,
      minSize: 110,
      maxSize: 130,
    },
    {
      accessorKey: "fiftyTwoWeekLowConverted",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="52W Min" />
        </div>
      ),
      cell: ({ row }) => {
        const company = row.original;
        const low = company.fiftyTwoWeekLowConverted;
        return (
          <div className="text-right font-medium truncate">
            {low !== undefined ? formatCurrency(low, displayCurrency) : "N/A"}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
      minSize: 100,
      maxSize: 120,
    },
    {
      accessorKey: "fiftyTwoWeekHighConverted",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="52W Max" />
        </div>
      ),
      cell: ({ row }) => {
        const company = row.original;
        const high = company.fiftyTwoWeekHighConverted;
        return (
          <div className="text-right font-medium truncate">
            {high !== undefined ? formatCurrency(high, displayCurrency) : "N/A"}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
      minSize: 100,
      maxSize: 120,
    },
    {
      accessorKey: "dividendYield",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Div Yield" />
        </div>
      ),
      cell: ({ row }) => {
        const dividendYield = row.original.dividendYield;
        return (
          <div className="text-right font-medium truncate">
            {dividendYield ? formatPercentage(dividendYield) : "N/A"}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
      minSize: 100,
      maxSize: 120,
    },
    {
      accessorKey: "costValue",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Valoare cost" />
        </div>
      ),
      cell: ({ row }) => {
        const costValue = row.getValue("costValue") as number;
        return (
          <div className="text-right font-medium truncate">
            {formatCurrency(costValue, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 130,
      minSize: 130,
      maxSize: 150,
    },
    {
      accessorKey: "marketValue",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Valoare piață" />
        </div>
      ),
      cell: ({ row }) => {
        const marketValue = row.getValue("marketValue") as number;
        return (
          <div className="text-right font-medium truncate">
            {formatCurrency(marketValue, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 130,
      minSize: 130,
      maxSize: 150,
    },
    {
      accessorKey: "dollarReturn",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="$ Return" />
        </div>
      ),
      cell: ({ row }) => {
        const dollarReturn = row.getValue("dollarReturn") as number;
        return (
          <div
            className={`text-right font-medium flex items-center justify-end gap-1 ${getReturnColor(
              dollarReturn
            )}`}
          >
            {dollarReturn > 0 ? (
              <TrendingUp className="h-3 w-3 flex-shrink-0" />
            ) : dollarReturn < 0 ? (
              <TrendingDown className="h-3 w-3 flex-shrink-0" />
            ) : null}
            <span className="truncate">
              {formatCurrency(dollarReturn, displayCurrency)}
            </span>
          </div>
        );
      },
      enableSorting: true,
      size: 120,
      minSize: 120,
      maxSize: 140,
    },
    {
      accessorKey: "percentReturn",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="% Return" />
        </div>
      ),
      cell: ({ row }) => {
        const percentReturn = row.getValue("percentReturn") as number;
        return (
          <div
            className={`text-right font-medium flex items-center justify-end gap-1 ${getReturnColor(
              percentReturn
            )}`}
          >
            {percentReturn > 0 ? (
              <TrendingUp className="h-3 w-3 flex-shrink-0" />
            ) : percentReturn < 0 ? (
              <TrendingDown className="h-3 w-3 flex-shrink-0" />
            ) : null}
            <span className="truncate">{formatPercentage(percentReturn)}</span>
          </div>
        );
      },
      enableSorting: true,
      size: 120,
      minSize: 120,
      maxSize: 140,
    },
    {
      accessorKey: "yesterdayChange",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Ieri" />
        </div>
      ),
      cell: ({ row }) => {
        const yesterdayChange = row.getValue("yesterdayChange") as number;
        return (
          <div
            className={`text-right font-medium truncate ${getReturnColor(
              yesterdayChange
            )}`}
          >
            {formatCurrency(yesterdayChange, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
      minSize: 100,
      maxSize: 120,
    },
    {
      accessorKey: "allocation",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Alocare" />
        </div>
      ),
      cell: ({ row }) => {
        const allocation = row.getValue("allocation") as number;
        return (
          <div className="text-right font-medium truncate">
            {formatPercentage(allocation)}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
      minSize: 100,
      maxSize: 120,
    },
    {
      accessorKey: "sector",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Sector" />
      ),
      cell: ({ row }) => {
        const sector = row.getValue("sector") as string;
        return (
          <div className="font-medium truncate" title={sector || "N/A"}>
            {sector || "N/A"}
          </div>
        );
      },
      enableSorting: true,
      size: 140,
      minSize: 140,
      maxSize: 180,
    },
    {
      accessorKey: "industry",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Industrie" />
      ),
      cell: ({ row }) => {
        const industry = row.getValue("industry") as string;
        return (
          <div className="font-medium truncate" title={industry || "N/A"}>
            {industry || "N/A"}
          </div>
        );
      },
      enableSorting: true,
      size: 140,
      minSize: 140,
      maxSize: 180,
    },
    {
      accessorKey: "country",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Țară" />
      ),
      cell: ({ row }) => {
        const country = row.getValue("country") as string;
        return (
          <div className="font-medium truncate" title={country || "N/A"}>
            {country || "N/A"}
          </div>
        );
      },
      enableSorting: true,
      size: 120,
      minSize: 120,
      maxSize: 140,
    },
    {
      accessorKey: "dividendIncome",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Dividende" />
        </div>
      ),
      cell: ({ row }) => {
        const dividendIncome = row.getValue("dividendIncome") as number;
        return (
          <div className="text-right font-medium truncate">
            {formatCurrency(dividendIncome, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 120,
      minSize: 120,
      maxSize: 140,
    },
    {
      accessorKey: "dividendPayments",
      header: ({ column }) => (
        <div className="text-right" title="Număr de plăți de dividende">
          <DataTableColumnHeader column={column} title="Nr. Plăți" />
        </div>
      ),
      cell: ({ row }) => {
        const dividendPayments = row.getValue("dividendPayments") as number;
        return (
          <div className="text-right font-medium truncate">
            {dividendPayments}
          </div>
        );
      },
      enableSorting: true,
      size: 100,
      minSize: 100,
      maxSize: 120,
    },
    {
      accessorKey: "totalTransactionFees",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Comisioane" />
        </div>
      ),
      cell: ({ row }) => {
        const totalTransactionFees = row.getValue(
          "totalTransactionFees"
        ) as number;
        return (
          <div className="text-right font-medium truncate">
            {formatCurrency(totalTransactionFees, displayCurrency)}
          </div>
        );
      },
      enableSorting: true,
      size: 120,
      minSize: 120,
      maxSize: 140,
    },
  ];
}
