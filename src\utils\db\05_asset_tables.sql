-- Asset Management Tables
-- Tables for storing asset information, prices, and metrics

-- Create asset table
CREATE TABLE IF NOT EXISTS ptvuser.asset (
    asset_id SERIAL PRIMARY KEY,
    ticker VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(500) NOT NULL,
    company VARCHAR(500) NOT NULL,
    isin VARCHAR(20),
    currency_id INTEGER REFERENCES ptvuser.currency(currency_id),
    exchange_id INTEGER REFERENCES ptvuser.exchange(exchange_id),
    sector_id INTEGER REFERENCES ptvuser.sector(sector_id),
    industry_id INTEGER REFERENCES ptvuser.industry(industry_id),
    country_id INTEGER REFERENCES ptvuser.country(country_id),
    asset_type_id INTEGER REFERENCES ptvuser.asset_type(asset_type_id),
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Validate ticker is not empty
    CONSTRAINT asset_ticker_check CHECK (LENGTH(TRIM(ticker)) > 0),
    -- Validate name is not empty
    CONSTRAINT asset_name_check CHECK (LENGTH(TRIM(name)) > 0),
    -- Validate company is not empty
    CONSTRAINT asset_company_check CHECK (LENGTH(TRIM(company)) > 0)
);

-- Create asset_prices table
CREATE TABLE IF NOT EXISTS ptvuser.asset_prices (
    price_id SERIAL PRIMARY KEY,
    asset_id INTEGER NOT NULL REFERENCES ptvuser.asset(asset_id) ON DELETE CASCADE,
    date DATE NOT NULL,
    open_price NUMERIC(20, 8),
    high_price NUMERIC(20, 8),
    low_price NUMERIC(20, 8),
    close_price NUMERIC(20, 8),
    adj_close NUMERIC(20, 8) NOT NULL,
    volume BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Unique constraint to prevent duplicate price entries for same asset and date
    CONSTRAINT asset_prices_asset_date_unique UNIQUE (asset_id, date),
    -- Validate prices are positive (if provided)
    CONSTRAINT asset_prices_open_positive CHECK (open_price IS NULL OR open_price >= 0),
    CONSTRAINT asset_prices_high_positive CHECK (high_price IS NULL OR high_price >= 0),
    CONSTRAINT asset_prices_low_positive CHECK (low_price IS NULL OR low_price >= 0),
    CONSTRAINT asset_prices_close_positive CHECK (close_price IS NULL OR close_price >= 0),
    CONSTRAINT asset_prices_adj_close_positive CHECK (adj_close >= 0),
    CONSTRAINT asset_prices_volume_positive CHECK (volume IS NULL OR volume >= 0)
);

-- Create asset_metrics table
CREATE TABLE IF NOT EXISTS ptvuser.asset_metrics (
    metrics_id SERIAL PRIMARY KEY,
    asset_id INTEGER NOT NULL REFERENCES ptvuser.asset(asset_id) ON DELETE CASCADE,
    date DATE NOT NULL,
    fifty_two_week_low NUMERIC(20, 8),
    fifty_two_week_high NUMERIC(20, 8),
    dividend_yield NUMERIC(10, 6),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Unique constraint to prevent duplicate metrics for same asset and date
    CONSTRAINT asset_metrics_asset_date_unique UNIQUE (asset_id, date),
    -- Validate metrics are positive (if provided)
    CONSTRAINT asset_metrics_52w_low_positive CHECK (fifty_two_week_low IS NULL OR fifty_two_week_low >= 0),
    CONSTRAINT asset_metrics_52w_high_positive CHECK (fifty_two_week_high IS NULL OR fifty_two_week_high >= 0),
    CONSTRAINT asset_metrics_dividend_yield_positive CHECK (dividend_yield IS NULL OR dividend_yield >= 0)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_asset_ticker ON ptvuser.asset(ticker);
CREATE INDEX IF NOT EXISTS idx_asset_name ON ptvuser.asset(name);
CREATE INDEX IF NOT EXISTS idx_asset_company ON ptvuser.asset(company);
CREATE INDEX IF NOT EXISTS idx_asset_currency_id ON ptvuser.asset(currency_id);
CREATE INDEX IF NOT EXISTS idx_asset_exchange_id ON ptvuser.asset(exchange_id);
CREATE INDEX IF NOT EXISTS idx_asset_sector_id ON ptvuser.asset(sector_id);
CREATE INDEX IF NOT EXISTS idx_asset_industry_id ON ptvuser.asset(industry_id);
CREATE INDEX IF NOT EXISTS idx_asset_country_id ON ptvuser.asset(country_id);
CREATE INDEX IF NOT EXISTS idx_asset_asset_type_id ON ptvuser.asset(asset_type_id);

CREATE INDEX IF NOT EXISTS idx_asset_prices_asset_id ON ptvuser.asset_prices(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_prices_date ON ptvuser.asset_prices(date DESC);
CREATE INDEX IF NOT EXISTS idx_asset_prices_asset_date ON ptvuser.asset_prices(asset_id, date DESC);

CREATE INDEX IF NOT EXISTS idx_asset_metrics_asset_id ON ptvuser.asset_metrics(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_metrics_date ON ptvuser.asset_metrics(date DESC);
CREATE INDEX IF NOT EXISTS idx_asset_metrics_asset_date ON ptvuser.asset_metrics(asset_id, date DESC);

-- Create updated_at triggers
CREATE TRIGGER update_asset_updated_at
    BEFORE UPDATE ON ptvuser.asset
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_asset_prices_updated_at
    BEFORE UPDATE ON ptvuser.asset_prices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_asset_metrics_updated_at
    BEFORE UPDATE ON ptvuser.asset_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE ptvuser.asset IS 'Master table for all tradeable assets (stocks, ETFs, etc.)';
COMMENT ON COLUMN ptvuser.asset.asset_id IS 'Unique identifier for the asset';
COMMENT ON COLUMN ptvuser.asset.ticker IS 'Asset ticker symbol (e.g., AAPL, MSFT)';
COMMENT ON COLUMN ptvuser.asset.name IS 'Full asset name (e.g., "Apple Inc.")';
COMMENT ON COLUMN ptvuser.asset.company IS 'Company name';
COMMENT ON COLUMN ptvuser.asset.isin IS 'International Securities Identification Number';
COMMENT ON COLUMN ptvuser.asset.logo_url IS 'URL to company logo image';

COMMENT ON TABLE ptvuser.asset_prices IS 'Historical price data for assets';
COMMENT ON COLUMN ptvuser.asset_prices.price_id IS 'Unique identifier for the price record';
COMMENT ON COLUMN ptvuser.asset_prices.asset_id IS 'Reference to the asset';
COMMENT ON COLUMN ptvuser.asset_prices.date IS 'Date of the price data';
COMMENT ON COLUMN ptvuser.asset_prices.adj_close IS 'Adjusted closing price (required field)';
COMMENT ON COLUMN ptvuser.asset_prices.volume IS 'Trading volume for the day';

COMMENT ON TABLE ptvuser.asset_metrics IS 'Additional metrics and ratios for assets';
COMMENT ON COLUMN ptvuser.asset_metrics.metrics_id IS 'Unique identifier for the metrics record';
COMMENT ON COLUMN ptvuser.asset_metrics.asset_id IS 'Reference to the asset';
COMMENT ON COLUMN ptvuser.asset_metrics.date IS 'Date of the metrics data';
COMMENT ON COLUMN ptvuser.asset_metrics.fifty_two_week_low IS '52-week low price';
COMMENT ON COLUMN ptvuser.asset_metrics.fifty_two_week_high IS '52-week high price';
COMMENT ON COLUMN ptvuser.asset_metrics.dividend_yield IS 'Current dividend yield percentage';
