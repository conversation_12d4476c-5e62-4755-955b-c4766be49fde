import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { auth } from "@/lib/auth";
import { getPortfolioDividendsData } from "@/utils/db/dashboard-queries";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import { z } from "zod";

// Validation schema for portfolio dividends request
const portfolioDividendsSchema = z.object({
  portfolioIds: z
    .array(z.string().uuid("ID-ul portofoliului trebuie să fie un UUID valid"))
    .min(1, "Cel puțin un portofoliu trebuie selectat"),
  displayCurrency: z
    .enum(["EUR", "USD", "RON"])
    .default("EUR")
    .optional(),
});

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Nu sunteți autentificat" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const portfolioIdsParam = searchParams.get("portfolioIds");
    const displayCurrency = (searchParams.get("displayCurrency") ||
      "EUR") as SupportedCurrency;

    if (!portfolioIdsParam) {
      return NextResponse.json(
        { error: "Portfolio IDs sunt necesare" },
        { status: 400 }
      );
    }

    const portfolioIds = portfolioIdsParam.split(",").filter(Boolean);

    const data = await getPortfolioDividendsData(portfolioIds, displayCurrency);

    return NextResponse.json({
      success: true,
      data,
      message: "Datele de dividende au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error in portfolio dividends API:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Eroare la încărcarea datelor de dividende",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Nu sunteți autentificat" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validationResult = portfolioDividendsSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Date de intrare invalide",
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { portfolioIds, displayCurrency = "EUR" } = validationResult.data;

    const data = await getPortfolioDividendsData(
      portfolioIds,
      displayCurrency as SupportedCurrency
    );

    return NextResponse.json({
      success: true,
      data,
      message: "Datele de dividende au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error in portfolio dividends API:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Eroare la încărcarea datelor de dividende",
      },
      { status: 500 }
    );
  }
}
