/**
 * Test suite for calculatePortfolioHoldingsAtDate function
 * Run with: node src/tests/test-portfolio-holdings-at-date.js
 */

// Mock the dashboard-queries module functions we need
function calculatePortfolioHoldingsAtDate(transactions, targetDate) {
  const holdings = new Map();

  // Filter transactions up to target date and sort by date
  const relevantTransactions = transactions
    .filter((t) => t.transaction_date <= targetDate)
    .sort(
      (a, b) =>
        new Date(a.transaction_date).getTime() -
        new Date(b.transaction_date).getTime()
    );

  relevantTransactions.forEach((transaction) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || { quantity: 0, costBasis: 0 };

    if (transaction.transaction_type === "BUY") {
      const newQuantity = existing.quantity + transaction.quantity;
      const newCostBasis =
        existing.costBasis + transaction.quantity * transaction.price;
      holdings.set(ticker, { quantity: newQuantity, costBasis: newCostBasis });
    } else if (transaction.transaction_type === "SELL") {
      const newQuantity = existing.quantity - transaction.quantity;
      const avgCostPerShare = existing.quantity > 0 ? existing.costBasis / existing.quantity : 0;
      const newCostBasis = newQuantity * avgCostPerShare;
      holdings.set(ticker, { quantity: newQuantity, costBasis: newCostBasis });
    }
  });

  return holdings;
}

// Test data
const mockTransactions = [
  {
    id: "1",
    ticker: "AAPL",
    transaction_type: "BUY",
    quantity: 10,
    price: 150.00,
    transaction_date: "2024-01-15",
  },
  {
    id: "2",
    ticker: "AAPL",
    transaction_type: "BUY",
    quantity: 5,
    price: 160.00,
    transaction_date: "2024-02-15",
  },
  {
    id: "3",
    ticker: "AAPL",
    transaction_type: "SELL",
    quantity: 3,
    price: 170.00,
    transaction_date: "2024-03-15",
  },
  {
    id: "4",
    ticker: "NVDA",
    transaction_type: "BUY",
    quantity: 20,
    price: 800.00,
    transaction_date: "2024-01-20",
  },
  {
    id: "5",
    ticker: "NVDA",
    transaction_type: "SELL",
    quantity: 5,
    price: 900.00,
    transaction_date: "2024-04-20",
  },
];

// Test functions
function assertEqual(actual, expected, testName) {
  if (Math.abs(actual - expected) < 0.01) {
    console.log(`✅ PASS: ${testName}`);
    return true;
  } else {
    console.log(`❌ FAIL: ${testName}`);
    console.log(`   Expected: ${expected}, Actual: ${actual}`);
    return false;
  }
}

function assertMapEqual(actualMap, expectedMap, testName) {
  let passed = true;
  
  // Check if maps have same size
  if (actualMap.size !== expectedMap.size) {
    console.log(`❌ FAIL: ${testName} - Map sizes differ`);
    console.log(`   Expected size: ${expectedMap.size}, Actual size: ${actualMap.size}`);
    return false;
  }

  // Check each key-value pair
  for (const [key, expectedValue] of expectedMap) {
    const actualValue = actualMap.get(key);
    if (!actualValue) {
      console.log(`❌ FAIL: ${testName} - Missing key: ${key}`);
      passed = false;
      continue;
    }

    if (Math.abs(actualValue.quantity - expectedValue.quantity) >= 0.01) {
      console.log(`❌ FAIL: ${testName} - ${key} quantity mismatch`);
      console.log(`   Expected: ${expectedValue.quantity}, Actual: ${actualValue.quantity}`);
      passed = false;
    }

    if (Math.abs(actualValue.costBasis - expectedValue.costBasis) >= 0.01) {
      console.log(`❌ FAIL: ${testName} - ${key} costBasis mismatch`);
      console.log(`   Expected: ${expectedValue.costBasis}, Actual: ${actualValue.costBasis}`);
      passed = false;
    }
  }

  if (passed) {
    console.log(`✅ PASS: ${testName}`);
  }
  return passed;
}

// Run tests
console.log("🧪 Testing calculatePortfolioHoldingsAtDate function\n");

// Test 1: Holdings at date before any transactions
console.log("Test 1: Holdings before any transactions");
const holdings1 = calculatePortfolioHoldingsAtDate(mockTransactions, "2024-01-01");
const expected1 = new Map();
assertMapEqual(holdings1, expected1, "Empty holdings before transactions");

// Test 2: Holdings after first AAPL purchase
console.log("\nTest 2: Holdings after first AAPL purchase");
const holdings2 = calculatePortfolioHoldingsAtDate(mockTransactions, "2024-01-16");
const expected2 = new Map([
  ["AAPL", { quantity: 10, costBasis: 1500.00 }]
]);
assertMapEqual(holdings2, expected2, "AAPL holdings after first purchase");

// Test 3: Holdings after both AAPL purchases and NVDA purchase
console.log("\nTest 3: Holdings after multiple purchases");
const holdings3 = calculatePortfolioHoldingsAtDate(mockTransactions, "2024-02-20");
const expected3 = new Map([
  ["AAPL", { quantity: 15, costBasis: 2300.00 }], // 10*150 + 5*160
  ["NVDA", { quantity: 20, costBasis: 16000.00 }]  // 20*800
]);
assertMapEqual(holdings3, expected3, "Holdings after multiple purchases");

// Test 4: Holdings after AAPL sale
console.log("\nTest 4: Holdings after AAPL sale");
const holdings4 = calculatePortfolioHoldingsAtDate(mockTransactions, "2024-03-20");
const expected4 = new Map([
  ["AAPL", { quantity: 12, costBasis: 1840.00 }], // 12 * (2300/15) = 12 * 153.33
  ["NVDA", { quantity: 20, costBasis: 16000.00 }]
]);
assertMapEqual(holdings4, expected4, "Holdings after AAPL sale");

// Test 5: Holdings after NVDA sale
console.log("\nTest 5: Holdings after NVDA sale");
const holdings5 = calculatePortfolioHoldingsAtDate(mockTransactions, "2024-05-01");
const expected5 = new Map([
  ["AAPL", { quantity: 12, costBasis: 1840.00 }],
  ["NVDA", { quantity: 15, costBasis: 12000.00 }] // 15 * 800
]);
assertMapEqual(holdings5, expected5, "Holdings after NVDA sale");

// Test 6: Edge case - exact transaction date
console.log("\nTest 6: Holdings on exact transaction date");
const holdings6 = calculatePortfolioHoldingsAtDate(mockTransactions, "2024-01-15");
const expected6 = new Map([
  ["AAPL", { quantity: 10, costBasis: 1500.00 }]
]);
assertMapEqual(holdings6, expected6, "Holdings on exact transaction date");

// Test 7: Empty transactions array
console.log("\nTest 7: Empty transactions array");
const holdings7 = calculatePortfolioHoldingsAtDate([], "2024-05-01");
const expected7 = new Map();
assertMapEqual(holdings7, expected7, "Empty transactions array");

// Test 8: Single transaction
console.log("\nTest 8: Single transaction");
const singleTransaction = [mockTransactions[0]];
const holdings8 = calculatePortfolioHoldingsAtDate(singleTransaction, "2024-01-20");
const expected8 = new Map([
  ["AAPL", { quantity: 10, costBasis: 1500.00 }]
]);
assertMapEqual(holdings8, expected8, "Single transaction");

console.log("\n🏁 Test suite completed!");
