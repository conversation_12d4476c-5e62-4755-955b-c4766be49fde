FROM node:24-alpine as base
RUN apk add --no-cache g++ make py3-pip libc6-compat nano
WORKDIR /app
COPY package*.json ./
EXPOSE 3000

FROM base as builder
WORKDIR /app
COPY . .
RUN npm install --legacy-peer-deps
RUN npm run build

# Production stage
FROM base as production
RUN mkdir -p /opt/files
WORKDIR /app
ENV NODE_ENV=production
RUN npm ci --legacy-peer-deps
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
RUN chown nextjs:nodejs /opt/files
USER nextjs
# Copy built assets from the builder stage
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next.config.ts ./next.config.ts
COPY --from=builder /app/public ./public
# Start the application
CMD ["npm", "start"]

# Development stage
FROM base as development
RUN npm install --legacy-peer-deps
COPY . .
CMD ["npm", "run", "dev"] 
